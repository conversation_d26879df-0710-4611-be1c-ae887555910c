# 容器日志配置指南

## 概述

已配置应用在容器中将日志记录到 `/app/logs` 目录，支持日志轮转、多级别记录和持久化存储。

## 📁 日志文件结构

```
/app/logs/
├── app.log        # 主日志文件 (所有级别)
├── app.log.1      # 轮转备份文件
├── app.log.2      # 轮转备份文件
├── ...
├── error.log      # 错误日志文件 (仅 ERROR 级别)
├── error.log.1    # 错误日志备份文件
└── error.log.2    # 错误日志备份文件
```

## ⚙️ 配置详情

### 日志轮转设置
- **最大文件大小**: 10MB
- **备份文件数量**: 5 个
- **轮转策略**: 当文件达到 10MB 时自动轮转

### 日志级别
- **DEBUG**: 调试信息 (开发环境)
- **INFO**: 一般信息 (默认)
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 日志格式
```
时间戳 - 模块名 - 级别 - 消息
例如: 2023-12-01 10:30:45,123 - app.api.routes - INFO - 收到训练请求
```

## 🔧 环境变量配置

### docker-compose.yml
```yaml
environment:
  LOG_LEVEL: INFO          # 日志级别
  LOG_DIR: /app/logs       # 日志目录
```

### .env 文件
```bash
# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs
```

## 🐳 Docker 使用方法

### 1. 启动容器
```bash
# 使用默认配置启动
docker-compose up -d

# 指定日志级别启动
LOG_LEVEL=DEBUG docker-compose up -d
```

### 2. 查看日志文件
```bash
# 进入容器
docker exec -it <container_id> bash

# 查看日志目录
ls -la /app/logs/

# 实时查看主日志
tail -f /app/logs/app.log

# 实时查看错误日志
tail -f /app/logs/error.log
```

### 3. 从容器复制日志
```bash
# 复制主日志文件
docker cp <container_id>:/app/logs/app.log ./app.log

# 复制错误日志文件
docker cp <container_id>:/app/logs/error.log ./error.log

# 复制整个日志目录
docker cp <container_id>:/app/logs/ ./logs/
```

### 4. 搜索和过滤日志
```bash
# 搜索错误日志
docker exec -it <container_id> grep ERROR /app/logs/app.log

# 搜索今天的日志
docker exec -it <container_id> grep $(date +%Y-%m-%d) /app/logs/app.log

# 搜索特定模块的日志
docker exec -it <container_id> grep "app.api.routes" /app/logs/app.log

# 统计错误数量
docker exec -it <container_id> grep -c ERROR /app/logs/app.log
```

## 📊 日志持久化

### 卷挂载 (推荐)
在 `docker-compose.yml` 中已配置卷挂载：

```yaml
volumes:
  - ./logs:/app/logs    # 将容器内日志映射到主机
```

这样日志文件会保存在主机的 `./logs/` 目录中，即使容器重启也不会丢失。

### 查看主机上的日志
```bash
# 主机上查看日志
ls -la ./logs/
tail -f ./logs/app.log
tail -f ./logs/error.log
```

## 🔍 监控和分析

### 1. 实时监控
```bash
# 监控所有日志
docker exec -it <container_id> tail -f /app/logs/app.log

# 只监控错误
docker exec -it <container_id> tail -f /app/logs/error.log

# 监控多个文件
docker exec -it <container_id> tail -f /app/logs/app.log /app/logs/error.log
```

### 2. 日志分析
```bash
# 统计不同级别的日志数量
docker exec -it <container_id> bash -c "
  echo 'INFO:' $(grep -c INFO /app/logs/app.log)
  echo 'WARNING:' $(grep -c WARNING /app/logs/app.log)
  echo 'ERROR:' $(grep -c ERROR /app/logs/app.log)
"

# 查看最近的错误
docker exec -it <container_id> grep ERROR /app/logs/app.log | tail -10

# 按时间范围查看日志
docker exec -it <container_id> grep "2023-12-01 10:" /app/logs/app.log
```

### 3. 性能分析
```bash
# 查看响应时间相关日志
docker exec -it <container_id> grep "响应时间\|response_time" /app/logs/app.log

# 查看数据库相关日志
docker exec -it <container_id> grep -i "mysql\|database" /app/logs/app.log

# 查看 API 调用日志
docker exec -it <container_id> grep "api/v1" /app/logs/app.log
```

## 🚨 告警和监控

### 1. 错误告警脚本
```bash
#!/bin/bash
# error_alert.sh - 检查错误日志并发送告警

ERROR_COUNT=$(docker exec <container_id> grep -c ERROR /app/logs/app.log)
THRESHOLD=10

if [ $ERROR_COUNT -gt $THRESHOLD ]; then
    echo "🚨 警告: 发现 $ERROR_COUNT 个错误，超过阈值 $THRESHOLD"
    # 发送告警通知 (邮件、Slack 等)
fi
```

### 2. 日志轮转监控
```bash
#!/bin/bash
# log_rotation_check.sh - 检查日志轮转状态

LOG_SIZE=$(docker exec <container_id> stat -c%s /app/logs/app.log)
MAX_SIZE=$((10 * 1024 * 1024))  # 10MB

if [ $LOG_SIZE -gt $MAX_SIZE ]; then
    echo "⚠️ 日志文件过大: $(($LOG_SIZE / 1024 / 1024))MB"
fi
```

## 🔧 故障排除

### 常见问题

#### 1. 日志文件不存在
```bash
# 检查目录权限
docker exec -it <container_id> ls -la /app/
docker exec -it <container_id> ls -la /app/logs/

# 检查应用是否正常启动
docker logs <container_id>
```

#### 2. 日志没有内容
```bash
# 检查日志级别设置
docker exec -it <container_id> env | grep LOG_LEVEL

# 手动触发一些日志
curl http://localhost:8001/health
curl http://localhost:8001/api/v1/info
```

#### 3. 权限问题
```bash
# 检查文件权限
docker exec -it <container_id> ls -la /app/logs/

# 修复权限 (如果需要)
docker exec -it <container_id> chown -R app:app /app/logs/
```

### 调试命令
```bash
# 检查日志配置
docker exec -it <container_id> python -c "
import logging
print('Root logger level:', logging.getLogger().level)
print('Handlers:', [h.__class__.__name__ for h in logging.getLogger().handlers])
"

# 测试日志写入
docker exec -it <container_id> python -c "
import logging
logging.info('Test log message')
print('Log message sent')
"
```

## 📈 最佳实践

### 1. 日志级别使用
- **生产环境**: INFO 或 WARNING
- **测试环境**: DEBUG
- **开发环境**: DEBUG

### 2. 日志内容
- 记录关键操作和状态变化
- 包含足够的上下文信息
- 避免记录敏感信息 (密码、API Key)

### 3. 监控策略
- 定期检查错误日志
- 监控日志文件大小
- 设置合理的告警阈值

### 4. 存储管理
- 定期清理旧的日志文件
- 考虑使用外部日志系统 (ELK Stack, Fluentd)
- 备份重要的日志文件

## 🔗 集成外部日志系统

### ELK Stack 集成
```yaml
# docker-compose.yml 中添加 Filebeat
filebeat:
  image: docker.elastic.co/beats/filebeat:7.15.0
  volumes:
    - ./logs:/app/logs:ro
    - ./filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
```

### Fluentd 集成
```yaml
# docker-compose.yml 中添加 Fluentd
fluentd:
  image: fluent/fluentd:v1.14
  volumes:
    - ./logs:/app/logs:ro
    - ./fluent.conf:/fluentd/etc/fluent.conf
```

## 总结

通过这个配置，你的容器现在具备了：

- ✅ **结构化日志记录** - 统一的格式和级别
- ✅ **自动日志轮转** - 防止磁盘空间耗尽
- ✅ **持久化存储** - 日志不会因容器重启而丢失
- ✅ **多输出目标** - 文件和控制台同时输出
- ✅ **灵活配置** - 通过环境变量控制
- ✅ **监控友好** - 便于集成监控系统

现在你可以通过 `/app/logs` 目录轻松管理和监控应用日志了！
