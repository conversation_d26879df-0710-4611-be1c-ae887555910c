#!/usr/bin/env python3
"""
MySQL 连接测试演示
展示如何使用新的连接测试接口
"""
import requests
import json


def demo_connection_test():
    """演示连接测试功能"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🔗 MySQL 连接测试演示")
    print("="*50)
    
    # 提示用户输入 API Key
    print("请输入你的 API Key:")
    api_key = input("API Key: ").strip()
    
    if not api_key:
        print("❌ 未提供 API Key，使用示例密钥")
        api_key = "your-api-key-here"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 1. 测试默认配置
    print("\n1️⃣ 测试默认配置连接...")
    try:
        response = requests.post(
            f"{base_url}/mysql/test-connection",
            headers=headers,
            json={},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            mysql_config = data.get('mysql_config', {})
            server_info = data.get('server_info', {})
            
            print("✅ 连接成功!")
            print(f"MySQL 服务器: {mysql_config.get('host')}:{mysql_config.get('port')}")
            print(f"数据库: {mysql_config.get('database')}")
            print(f"用户: {mysql_config.get('user')}")
            print(f"服务器版本: {server_info.get('version', 'N/A')}")
            print(f"响应时间: {data.get('response_time_ms', 'N/A')}ms")
            print(f"数据库存在: {'是' if data.get('database_exists') else '否'}")
        else:
            error_info = response.json()
            print("❌ 连接失败")
            print(f"错误: {error_info.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 2. 交互式测试自定义配置
    print(f"\n2️⃣ 测试自定义配置连接...")
    print("请输入 MySQL 连接参数 (按回车使用默认值):")
    
    host = input("主机地址 [localhost]: ").strip() or "localhost"
    port_input = input("端口 [3306]: ").strip()
    port = int(port_input) if port_input.isdigit() else 3306
    user = input("用户名 [root]: ").strip() or "root"
    password = input("密码 []: ").strip()
    database = input("数据库名 [test]: ").strip() or "test"
    
    custom_config = {
        "mysql_host": host,
        "mysql_port": port,
        "mysql_user": user,
        "mysql_database": database
    }
    
    if password:
        custom_config["mysql_password"] = password
    
    print(f"\n测试配置: {host}:{port}, 用户: {user}, 数据库: {database}")
    
    try:
        response = requests.post(
            f"{base_url}/mysql/test-connection",
            headers=headers,
            json=custom_config,
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            server_info = data.get('server_info', {})
            
            print("✅ 连接成功!")
            print(f"服务器版本: {server_info.get('version', 'N/A')}")
            print(f"响应时间: {data.get('response_time_ms', 'N/A')}ms")
            print(f"数据库存在: {'是' if data.get('database_exists') else '否'}")
            
            if not data.get('database_exists'):
                print("⚠️  注意: 指定的数据库不存在，但连接到服务器成功")
                
        else:
            error_info = response.json()
            print("❌ 连接失败")
            print(f"错误: {error_info.get('message', 'Unknown error')}")
            
            # 提供故障排除建议
            print("\n🔧 故障排除建议:")
            error_msg = error_info.get('message', '').lower()
            if 'timeout' in error_msg or 'connect' in error_msg:
                print("- 检查主机地址和端口是否正确")
                print("- 确认 MySQL 服务器正在运行")
                print("- 检查网络连接和防火墙设置")
            elif 'password' in error_msg or 'access denied' in error_msg:
                print("- 检查用户名和密码是否正确")
                print("- 确认用户具有连接权限")
            elif 'database' in error_msg:
                print("- 检查数据库名称是否正确")
                print("- 确认数据库存在且用户有访问权限")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 3. 显示使用建议
    print(f"\n" + "="*50)
    print("💡 使用建议:")
    print("1. 在使用 train 和 ask 接口前，先用此接口测试连接")
    print("2. 此接口只测试连接，不会执行任何数据操作")
    print("3. 可以用于验证不同环境的数据库配置")
    print("4. 响应时间可以帮助评估网络延迟")
    
    print(f"\n📋 相关接口:")
    print("- POST /api/v1/mysql/test-connection - 测试连接")
    print("- POST /api/v1/train - 训练模型 (支持 MySQL 参数)")
    print("- POST /api/v1/ask - 查询 (支持 MySQL 参数)")


def show_curl_examples():
    """显示 curl 使用示例"""
    print(f"\n" + "="*50)
    print("📚 curl 使用示例")
    print("="*50)
    
    print("\n1. 测试默认配置:")
    print("""
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{}'
""")
    
    print("\n2. 测试自定义配置:")
    print("""
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "mysql_host": "localhost",
    "mysql_port": 3306,
    "mysql_user": "test_user",
    "mysql_password": "test_password",
    "mysql_database": "test_db"
  }'
""")
    
    print("\n3. 只测试服务器连接 (不指定数据库):")
    print("""
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "mysql_host": "production-db.company.com",
    "mysql_user": "readonly_user"
  }'
""")


if __name__ == '__main__':
    try:
        demo_connection_test()
        show_curl_examples()
    except KeyboardInterrupt:
        print("\n\n演示已取消")
    except Exception as e:
        print(f"\n❌ 演示错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行")
        print("2. 已配置有效的 API Key")
        print("3. 网络连接正常")
