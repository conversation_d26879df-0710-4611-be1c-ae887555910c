#!/usr/bin/env python3
"""
测试 train API 的日志功能
验证详细的日志记录是否正常工作
"""
import requests
import json
import time


def test_train_api_with_logs():
    """测试 train API 并观察日志输出"""
    print("📝 测试 Train API 日志功能")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8000/api/v1"
    api_key = input("请输入你的 API Key: ").strip()
    
    if not api_key:
        print("❌ 未提供 API Key")
        return
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "基础 DDL 训练",
            "data": {
                "ddl": "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(100), age INT);",
                "documentation": "用户表，存储用户的基本信息"
            }
        },
        {
            "name": "SQL 示例训练",
            "data": {
                "sql": "SELECT * FROM users WHERE age > 18;",
                "documentation": "查询成年用户"
            }
        },
        {
            "name": "问题-SQL 对训练",
            "data": {
                "question_sql_pairs": [
                    {
                        "question": "查询所有用户",
                        "sql": "SELECT * FROM users;"
                    },
                    {
                        "question": "查询用户数量",
                        "sql": "SELECT COUNT(*) FROM users;"
                    }
                ]
            }
        },
        {
            "name": "综合训练（包含自定义 MySQL 配置）",
            "data": {
                "ddl": "CREATE TABLE products (id INT, name VARCHAR(100), price DECIMAL(10,2));",
                "documentation": "产品表",
                "sql": "SELECT * FROM products WHERE price > 100;",
                "question_sql_pairs": [
                    {
                        "question": "查询高价产品",
                        "sql": "SELECT * FROM products WHERE price > 100;"
                    }
                ],
                "mysql_host": "localhost",
                "mysql_port": 3306,
                "mysql_user": "test_user",
                "mysql_password": "test_password",
                "mysql_database": "test_db"
            }
        },
        {
            "name": "空参数测试（应该失败）",
            "data": {}
        },
        {
            "name": "无效 MySQL 配置测试",
            "data": {
                "ddl": "CREATE TABLE test (id INT);",
                "mysql_host": "",  # 空主机名
                "mysql_user": "",  # 空用户名
                "mysql_database": ""  # 空数据库名
            }
        }
    ]
    
    print(f"\n📋 将执行 {len(test_cases)} 个测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"  {i}. {case['name']}")
    
    # 让用户选择测试用例
    print(f"\n请选择:")
    print("0: 执行所有测试用例")
    print("1-6: 执行特定测试用例")
    
    choice = input("请输入选择: ").strip()
    
    if choice == "0":
        selected_cases = test_cases
    elif choice in ["1", "2", "3", "4", "5", "6"]:
        selected_cases = [test_cases[int(choice) - 1]]
    else:
        print("❌ 无效选择")
        return
    
    # 执行测试
    for i, test_case in enumerate(selected_cases, 1):
        print(f"\n" + "="*60)
        print(f"🧪 测试用例 {i}: {test_case['name']}")
        print("="*60)
        
        print(f"📤 发送请求...")
        print(f"URL: {base_url}/train")
        print(f"Data: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            # 发送请求
            start_time = time.time()
            response = requests.post(
                f"{base_url}/train",
                headers=headers,
                json=test_case['data'],
                timeout=30
            )
            end_time = time.time()
            
            print(f"\n📥 响应信息:")
            print(f"状态码: {response.status_code}")
            print(f"响应时间: {end_time - start_time:.2f} 秒")
            print(f"响应头: {dict(response.headers)}")
            
            # 解析响应
            try:
                response_data = response.json()
                print(f"\n📄 响应内容:")
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                if response.status_code == 200:
                    print(f"\n✅ 测试成功!")
                    
                    # 分析响应数据
                    if 'data' in response_data:
                        data = response_data['data']
                        if 'training_data_summary' in data:
                            summary = data['training_data_summary']
                            print(f"\n📊 训练数据摘要:")
                            print(f"  DDL 提供: {summary.get('ddl_provided', False)}")
                            print(f"  Documentation 提供: {summary.get('documentation_provided', False)}")
                            print(f"  SQL 提供: {summary.get('sql_provided', False)}")
                            print(f"  Question-SQL 对数量: {summary.get('question_sql_pairs_count', 0)}")
                        
                        if 'thread_id' in data:
                            print(f"  训练线程 ID: {data['thread_id']}")
                        
                        if 'mysql_config' in data:
                            mysql_config = data['mysql_config']
                            print(f"\n🗄️  MySQL 配置:")
                            print(f"  Host: {mysql_config.get('host', 'N/A')}")
                            print(f"  Port: {mysql_config.get('port', 'N/A')}")
                            print(f"  User: {mysql_config.get('user', 'N/A')}")
                            print(f"  Database: {mysql_config.get('database', 'N/A')}")
                    
                    # 提示查看日志
                    print(f"\n💡 提示:")
                    print("请查看服务器日志以观察详细的训练过程:")
                    print("  docker-compose logs -f vanna-app")
                    print("  或")
                    print("  tail -f logs/app.log")
                    
                else:
                    print(f"\n❌ 测试失败!")
                    if 'message' in response_data:
                        print(f"错误信息: {response_data['message']}")
                    if 'error_code' in response_data:
                        print(f"错误代码: {response_data['error_code']}")
                        
            except json.JSONDecodeError:
                print(f"\n❌ 响应不是有效的 JSON:")
                print(response.text[:500])
                
        except requests.exceptions.Timeout:
            print(f"\n❌ 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"\n❌ 连接错误，请确保服务器正在运行")
        except Exception as e:
            print(f"\n❌ 请求异常: {str(e)}")
        
        # 如果是多个测试用例，询问是否继续
        if len(selected_cases) > 1 and i < len(selected_cases):
            continue_choice = input(f"\n继续下一个测试用例？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes']:
                break
    
    print(f"\n🎉 测试完成!")
    print(f"\n📋 日志查看命令:")
    print("# 实时查看应用日志")
    print("docker-compose logs -f vanna-app")
    print("\n# 查看最近的日志")
    print("docker-compose logs --tail=100 vanna-app")
    print("\n# 搜索训练相关日志")
    print("docker-compose logs vanna-app | grep -E '(训练|train|Train)'")


def show_log_analysis_tips():
    """显示日志分析提示"""
    print(f"\n" + "="*50)
    print("📊 日志分析提示")
    print("="*50)
    
    print(f"\n🔍 关键日志标识:")
    print("  🎓 - 训练请求开始")
    print("  📋 - 参数信息")
    print("  🗄️  - MySQL 配置")
    print("  🤖 - AI 配置")
    print("  🚀 - 异步训练启动")
    print("  ✅ - 成功完成")
    print("  ❌ - 错误发生")
    
    print(f"\n📝 日志级别:")
    print("  INFO - 正常流程信息")
    print("  WARNING - 警告信息")
    print("  ERROR - 错误信息")
    
    print(f"\n🔧 常用日志命令:")
    print("# 查看所有日志")
    print("docker-compose logs vanna-app")
    
    print("\n# 实时跟踪日志")
    print("docker-compose logs -f vanna-app")
    
    print("\n# 查看最近 50 行日志")
    print("docker-compose logs --tail=50 vanna-app")
    
    print("\n# 搜索错误日志")
    print("docker-compose logs vanna-app | grep ERROR")
    
    print("\n# 搜索训练相关日志")
    print("docker-compose logs vanna-app | grep -E '(🎓|训练|train)'")
    
    print("\n# 按时间过滤日志")
    print("docker-compose logs --since='2023-12-01T10:00:00' vanna-app")


if __name__ == '__main__':
    try:
        test_train_api_with_logs()
        show_log_analysis_tips()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试脚本错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行在 http://localhost:8000")
        print("2. 已配置有效的 API Key")
        print("3. 网络连接正常")
