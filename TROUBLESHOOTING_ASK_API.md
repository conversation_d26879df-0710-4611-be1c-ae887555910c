# Ask API 错误排查指南

## 错误信息
```
"50 - app.api.routes - ERROR - 查询错误: expected string or bytes-like object, got 'NoneType'"
```

## 可能的原因和解决方案

### 1. 🔍 **最常见原因：字符串操作中的 None 值**

#### 可能的位置：
- `self.vn.generate_sql(question=question)` 返回 `None`
- `self.vn.generate_plotly_code()` 的参数中有 `None` 值
- DataFrame 操作中的 `None` 值

#### 解决方案：
```python
# 在 app/core/vanna_client.py 的 ask 方法中添加检查
sql = self.vn.generate_sql(question=question)
if not sql:
    raise ValueError("无法生成 SQL 查询")
```

### 2. 🗄️ **数据库连接问题**

#### 检查步骤：
```bash
# 测试数据库连接
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "mysql_host": "localhost",
    "mysql_port": 3306,
    "mysql_user": "your-user",
    "mysql_password": "your-password",
    "mysql_database": "your-database"
  }'
```

#### 常见问题：
- MySQL 服务未启动
- 连接参数错误
- 权限不足
- 网络连接问题

### 3. 🤖 **AI API 配置问题**

#### 检查环境变量：
```bash
# 检查 API Key 配置
echo $OPEN_API_KEY
echo $OPEN_MODEL_NAME
echo $OPEN_BASE_URL
```

#### 常见问题：
- API Key 无效或过期
- 模型名称错误
- Base URL 配置错误
- API 配额不足

### 4. 📊 **数据训练问题**

#### 检查是否已训练：
- 数据库表结构是否已训练
- 是否有足够的训练数据
- 问题描述是否清晰

#### 解决方案：
```bash
# 先进行训练
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100), email VARCHAR(100));",
    "documentation": "用户表，包含用户基本信息"
  }'
```

## 调试步骤

### 第一步：检查基础服务
```bash
# 1. 检查服务状态
curl http://localhost:8000/health

# 2. 检查 API 信息
curl http://localhost:8000/api/v1/info
```

### 第二步：检查数据库连接
```bash
# 测试数据库连接
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 第三步：查看详细日志
```bash
# 查看应用日志
docker-compose logs -f vanna-app

# 或者查看文件日志
tail -f logs/app.log
```

### 第四步：使用调试脚本
```bash
# 运行调试脚本
python debug_ask_api.py

# 或者直接测试 VannaClient
python test_vanna_client.py
```

## 代码修复

### 1. 增强错误处理（已实现）

在 `app/core/vanna_client.py` 中：
```python
def ask(self, question):
    # 添加参数检查
    if not question or not isinstance(question, str):
        raise ValueError("问题不能为空且必须是字符串")
    
    # 添加 SQL 生成检查
    sql = self.vn.generate_sql(question=question)
    if not sql:
        raise ValueError("无法生成 SQL 查询")
    
    # 添加 SQL 执行检查
    df = self.vn.run_sql(sql)
    if df is None:
        raise ValueError("SQL 执行失败")
```

### 2. 增强日志记录（已实现）

添加详细的日志记录来追踪问题：
```python
import logging
logger = logging.getLogger(__name__)

logger.info(f"开始处理问题: {question}")
logger.info(f"生成的 SQL: {sql}")
logger.info(f"查询结果形状: {df.shape}")
```

### 3. API 层错误处理（已实现）

在 `app/api/routes.py` 中：
```python
try:
    result = vanna_client.ask(question)
except ValueError as ve:
    return error_response(f"查询参数错误: {str(ve)}", "QUERY_PARAM_ERROR", 400)
except RuntimeError as re:
    return error_response(f"查询运行时错误: {str(re)}", "QUERY_RUNTIME_ERROR", 500)
```

## 常见解决方案

### 方案 1：重新初始化
```bash
# 重启服务
docker-compose restart vanna-app

# 或者重新构建
docker-compose build vanna-app
docker-compose up -d vanna-app
```

### 方案 2：检查配置
```bash
# 检查环境变量
docker exec vanna-texttosql-server env | grep -E "(MYSQL|OPEN_)"

# 检查配置文件
docker exec vanna-texttosql-server cat /app/.env
```

### 方案 3：清理数据
```bash
# 清理 ChromaDB 数据（谨慎使用）
rm -rf chromadb/*

# 重新训练
# 使用 /api/v1/train 端点重新训练模型
```

## 预防措施

### 1. 配置验证
- 确保所有必需的环境变量都已设置
- 定期检查 API Key 的有效性
- 验证数据库连接参数

### 2. 监控和日志
- 设置适当的日志级别
- 监控 API 响应时间
- 定期检查错误日志

### 3. 测试流程
- 在生产环境部署前进行充分测试
- 使用不同类型的问题进行测试
- 验证各种边界情况

## 联系支持

如果问题仍然存在，请提供以下信息：

1. **错误日志**：完整的错误堆栈信息
2. **配置信息**：环境变量和配置文件（隐藏敏感信息）
3. **测试请求**：导致错误的具体请求内容
4. **环境信息**：Docker 版本、系统信息等

### 收集调试信息的命令：
```bash
# 收集系统信息
docker --version
docker-compose --version

# 收集应用日志
docker-compose logs vanna-app > debug_logs.txt

# 收集配置信息
docker exec vanna-texttosql-server env | grep -v PASSWORD > debug_env.txt
```
