
#FROM python:3.11
FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/python:3.11.11-bookworm
# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=run.py \
    FLASK_ENV=production

# # 安装系统依赖
# RUN apt-get update && apt-get install -i https://pypi.tuna.tsinghua.edu.cn/simple  -y \
#     gcc \
#     g++ \
#     pkg-config \
#     default-libmysqlclient-dev \
#     && rm -rf /var/lib/apt/lists/*



# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
# RUN pip install --no-cache-dir --upgrade pip && \
#     pip install --no-cache-dir  --resume-retries 5 -r requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple --resume-retries 5 -r requirements.txt

RUN python -c "import chromadb; client = chromadb.Client(); client.get_or_create_collection('test')"

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/chromadb /app/data/csv /app/data/png /app/data/images /app/data/charts /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/

# 复制并解压 ONNX 模型文件
COPY onnx.tar.gz /tmp/onnx.tar.gz
RUN tar -xzf /tmp/onnx.tar.gz -C /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/ && \
    rm /tmp/onnx.tar.gz


# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "run.py"]
