#!/usr/bin/env python3
"""
测试训练数据管理 API
验证获取训练数据列表和删除训练数据的接口
"""
import requests
import json
import sys


def test_get_training_data():
    """测试获取训练数据列表接口"""
    print("📋 测试获取训练数据列表接口")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8001"
    api_key = "your-api-key-here"  # 请替换为实际的 API Key
    
    # 请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "基本获取训练数据",
            "url": f"{base_url}/api/v1/training-data",
            "params": {},
            "expected_status": 200
        },
        {
            "name": "带 MySQL 参数获取训练数据",
            "url": f"{base_url}/api/v1/training-data",
            "params": {
                "VANNA_MYSQL_host": "localhost",
                "VANNA_MYSQL_port": "3306",
                "VANNA_MYSQL_user": "vanna_user",
                "VANNA_MYSQL_password": "vanna_password",
                "VANNA_MYSQL_database": "vanna_db"
            },
            "expected_status": 200
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        
        try:
            # 发送请求
            response = requests.get(
                test_case['url'],
                headers=headers,
                params=test_case['params'],
                timeout=30
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            # 解析响应
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ 请求成功")
                    
                    if 'data' in data and 'training_data' in data['data']:
                        training_data = data['data']['training_data']
                        count = data['data'].get('count', 0)
                        print(f"   📊 训练数据数量: {count}")
                        
                        if training_data and len(training_data) > 0:
                            print(f"   📋 前3条训练数据:")
                            for j, item in enumerate(training_data[:3], 1):
                                print(f"      {j}. ID: {item.get('id', 'N/A')}")
                                print(f"         类型: {item.get('training_data_type', 'N/A')}")
                                if 'question' in item:
                                    print(f"         问题: {item['question'][:50]}...")
                                if 'content' in item:
                                    print(f"         内容: {str(item['content'])[:50]}...")
                        else:
                            print(f"   ℹ️  暂无训练数据")
                    
                    results.append({
                        'test': test_case['name'],
                        'status': 'success',
                        'data': data
                    })
                    
                except json.JSONDecodeError:
                    print(f"   ❌ JSON 解析失败")
                    print(f"   响应内容: {response.text[:200]}...")
                    results.append({
                        'test': test_case['name'],
                        'status': 'json_error',
                        'response': response.text
                    })
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
                results.append({
                    'test': test_case['name'],
                    'status': 'http_error',
                    'status_code': response.status_code,
                    'response': response.text
                })
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未运行")
            results.append({
                'test': test_case['name'],
                'status': 'connection_error'
            })
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
            results.append({
                'test': test_case['name'],
                'status': 'timeout'
            })
        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")
            results.append({
                'test': test_case['name'],
                'status': 'error',
                'error': str(e)
            })
    
    return results


def test_remove_training_data():
    """测试删除训练数据接口"""
    print(f"\n" + "="*50)
    print("🗑️  测试删除训练数据接口")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8001"
    api_key = "your-api-key-here"  # 请替换为实际的 API Key
    
    # 请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 首先获取训练数据列表，找到可以删除的数据
    print(f"\n1. 先获取训练数据列表...")
    
    try:
        response = requests.get(
            f"{base_url}/api/v1/training-data",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            training_data = data.get('data', {}).get('training_data', [])
            
            if training_data and len(training_data) > 0:
                # 选择第一个训练数据进行删除测试
                first_item = training_data[0]
                training_id = first_item.get('id')
                
                if training_id:
                    print(f"   ✅ 找到训练数据 ID: {training_id}")
                    print(f"   📋 数据类型: {first_item.get('training_data_type', 'N/A')}")
                    
                    # 测试删除
                    print(f"\n2. 测试删除训练数据...")
                    
                    delete_response = requests.delete(
                        f"{base_url}/api/v1/training-data/{training_id}",
                        headers=headers,
                        timeout=30
                    )
                    
                    print(f"   状态码: {delete_response.status_code}")
                    
                    if delete_response.status_code == 200:
                        delete_data = delete_response.json()
                        print(f"   ✅ 删除成功")
                        print(f"   📋 删除的 ID: {delete_data.get('data', {}).get('training_id', 'N/A')}")
                        return True
                    else:
                        print(f"   ❌ 删除失败: {delete_response.status_code}")
                        print(f"   响应: {delete_response.text[:200]}...")
                        return False
                else:
                    print(f"   ❌ 训练数据没有 ID 字段")
                    return False
            else:
                print(f"   ℹ️  没有训练数据可供删除")
                
                # 测试删除不存在的数据
                print(f"\n2. 测试删除不存在的训练数据...")
                fake_id = "non-existent-id"
                
                delete_response = requests.delete(
                    f"{base_url}/api/v1/training-data/{fake_id}",
                    headers=headers,
                    timeout=30
                )
                
                print(f"   状态码: {delete_response.status_code}")
                print(f"   响应: {delete_response.text[:200]}...")
                
                return delete_response.status_code in [404, 400, 500]  # 预期的错误状态码
        else:
            print(f"   ❌ 获取训练数据失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试删除训练数据异常: {str(e)}")
        return False


def show_api_usage():
    """显示 API 使用示例"""
    print(f"\n" + "="*50)
    print("📚 API 使用示例")
    print("="*50)
    
    print(f"\n1. 获取训练数据列表:")
    print(f"""
# 基本请求
curl -X GET "http://localhost:8001/api/v1/training-data" \\
  -H "Authorization: Bearer your-api-key"

# 带 MySQL 参数
curl -X GET "http://localhost:8001/api/v1/training-data?VANNA_MYSQL_host=localhost&VANNA_MYSQL_database=vanna_db" \\
  -H "Authorization: Bearer your-api-key"
""")
    
    print(f"\n2. 删除训练数据:")
    print(f"""
# 删除指定 ID 的训练数据
curl -X DELETE "http://localhost:8001/api/v1/training-data/training-id-here" \\
  -H "Authorization: Bearer your-api-key"

# 带 MySQL 参数删除
curl -X DELETE "http://localhost:8001/api/v1/training-data/training-id-here?VANNA_MYSQL_host=localhost" \\
  -H "Authorization: Bearer your-api-key"
""")
    
    print(f"\n3. Python 示例:")
    print(f"""
import requests

# 配置
base_url = "http://localhost:8001"
api_key = "your-api-key"
headers = {"Authorization": f"Bearer {{api_key}}"}

# 获取训练数据
response = requests.get(f"{{base_url}}/api/v1/training-data", headers=headers)
training_data = response.json()

# 删除训练数据
training_id = "some-training-id"
response = requests.delete(f"{{base_url}}/api/v1/training-data/{{training_id}}", headers=headers)
""")


def main():
    """主测试函数"""
    print("🧪 训练数据管理 API 测试")
    print("="*50)
    
    print(f"\n⚠️  注意事项:")
    print(f"   1. 请确保服务器正在运行 (http://localhost:8001)")
    print(f"   2. 请替换脚本中的 API Key")
    print(f"   3. 请确保有有效的 MySQL 连接配置")
    print(f"   4. 删除操作是不可逆的，请谨慎操作")
    
    # 测试获取训练数据
    get_results = test_get_training_data()
    
    # 测试删除训练数据
    delete_success = test_remove_training_data()
    
    # 显示使用示例
    show_api_usage()
    
    # 显示测试总结
    print(f"\n" + "="*50)
    print("📊 测试总结")
    print("="*50)
    
    get_success_count = len([r for r in get_results if r['status'] == 'success'])
    get_total_count = len(get_results)
    
    print(f"获取训练数据测试: {get_success_count}/{get_total_count} 成功")
    print(f"删除训练数据测试: {'✅ 成功' if delete_success else '❌ 失败'}")
    
    if get_success_count == get_total_count and delete_success:
        print(f"\n🎉 所有测试通过!")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查:")
        print(f"   - 服务器是否正在运行")
        print(f"   - API Key 是否正确")
        print(f"   - MySQL 配置是否有效")
        return False


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试脚本错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
