# Flask 应用配置
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-super-secret-key-change-this-in-production

# 应用端口
APP_PORT=8000

# API Key 认证配置
ENABLE_API_KEY_AUTH=true
API_KEYS=your-api-key-1,your-api-key-2,your-api-key-3

# MySQL 数据库配置
VANNA_MYSQL_ROOT_PASSWORD=rootpassword123
VANNA_MYSQL_DATABASE=vanna_db
VANNA_MYSQL_USER=vanna_user
VANNA_MYSQL_PASSWORD=vanna_password123
VANNA_MYSQL_PORT=3306

# OpenAI/DeepSeek API 配置
VANNA_OPEN_API_KEY=your-api-key-here
VANNA_OPEN_MODEL_NAME=deepseek-chat
VANNA_OPEN_BASE_URL=https://api.deepseek.com/v1

# 或者使用 OpenAI
# VANNA_OPEN_API_KEY=sk-your-openai-api-key
# VANNA_OPEN_MODEL_NAME=gpt-3.5-turbo
# VANNA_OPEN_BASE_URL=https://api.openai.com/v1
