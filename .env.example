# Flask 应用配置
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-super-secret-key-change-this-in-production

# 应用端口
APP_PORT=8000

# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=rootpassword123
MYSQL_DATABASE=vanna_db
MYSQL_USER=vanna_user
MYSQL_PASSWORD=vanna_password123
MYSQL_PORT=3306

# OpenAI/DeepSeek API 配置
OPEN_API_KEY=your-api-key-here
OPEN_MODEL_NAME=deepseek-chat
OPEN_BASE_URL=https://api.deepseek.com/v1

# 或者使用 OpenAI
# OPEN_API_KEY=sk-your-openai-api-key
# OPEN_MODEL_NAME=gpt-3.5-turbo
# OPEN_BASE_URL=https://api.openai.com/v1
