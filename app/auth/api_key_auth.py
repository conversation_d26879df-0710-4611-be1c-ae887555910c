"""
API Key 认证模块
支持多个 API Key 验证，通过请求头传递
"""
from functools import wraps
from flask import request, current_app
from app.utils.response import error_response
import logging
import hashlib
import hmac
import time

logger = logging.getLogger(__name__)


class APIKeyAuth:
    """API Key 认证类"""
    
    @staticmethod
    def get_valid_api_keys():
        """获取有效的 API Keys"""
        api_keys = current_app.config.get('API_KEYS', [])
        if isinstance(api_keys, str):
            # 如果是字符串，按逗号分割
            return [key.strip() for key in api_keys.split(',') if key.strip()]
        elif isinstance(api_keys, list):
            return api_keys
        else:
            return []
    
    @staticmethod
    def validate_api_key(api_key):
        """验证 API Key 是否有效"""
        if not api_key:
            return False
        
        valid_keys = APIKeyAuth.get_valid_api_keys()
        if not valid_keys:
            logger.warning("未配置任何有效的 API Keys")
            return False
        
        # 使用安全的字符串比较，防止时序攻击
        for valid_key in valid_keys:
            if hmac.compare_digest(api_key, valid_key):
                return True
        
        return False
    
    @staticmethod
    def extract_api_key_from_request():
        """从请求中提取 API Key"""
        # 优先从 Authorization 头获取
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            return auth_header[7:]  # 移除 "Bearer " 前缀
        
        # 从 X-API-Key 头获取
        api_key = request.headers.get('X-API-Key')
        if api_key:
            return api_key
        
        # 从 API-Key 头获取
        api_key = request.headers.get('API-Key')
        if api_key:
            return api_key
        
        return None
    
    @staticmethod
    def log_auth_attempt(api_key, success, ip_address, user_agent):
        """记录认证尝试"""
        masked_key = f"{api_key[:8]}..." if api_key and len(api_key) > 8 else "None"
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"API Key Auth {status}: key={masked_key}, ip={ip_address}, ua={user_agent}")


def require_api_key(f):
    """
    API Key 认证装饰器
    使用方法：
    @require_api_key
    def protected_route():
        return "This is protected"
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查是否启用了 API Key 认证
        if not current_app.config.get('ENABLE_API_KEY_AUTH', True):
            logger.debug("API Key 认证已禁用，跳过验证")
            return f(*args, **kwargs)
        
        # 提取 API Key
        api_key = APIKeyAuth.extract_api_key_from_request()
        
        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        user_agent = request.headers.get('User-Agent', 'Unknown')
        
        # 验证 API Key
        if not APIKeyAuth.validate_api_key(api_key):
            APIKeyAuth.log_auth_attempt(api_key, False, ip_address, user_agent)
            return error_response(
                message="无效的 API Key 或缺少认证信息",
                error_code="INVALID_API_KEY",
                status_code=401
            )
        
        # 记录成功的认证
        APIKeyAuth.log_auth_attempt(api_key, True, ip_address, user_agent)
        
        # 将 API Key 信息添加到请求上下文（可选）
        request.api_key = api_key
        
        return f(*args, **kwargs)
    
    return decorated_function


def optional_api_key(f):
    """
    可选的 API Key 认证装饰器
    如果提供了 API Key 则验证，如果没有提供则跳过
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = APIKeyAuth.extract_api_key_from_request()
        
        if api_key:
            # 如果提供了 API Key，则必须验证通过
            if not APIKeyAuth.validate_api_key(api_key):
                ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
                user_agent = request.headers.get('User-Agent', 'Unknown')
                APIKeyAuth.log_auth_attempt(api_key, False, ip_address, user_agent)
                return error_response(
                    message="提供的 API Key 无效",
                    error_code="INVALID_API_KEY",
                    status_code=401
                )
            request.api_key = api_key
        else:
            request.api_key = None
        
        return f(*args, **kwargs)
    
    return decorated_function


class APIKeyManager:
    """API Key 管理工具类"""
    
    @staticmethod
    def generate_api_key(length=32):
        """生成新的 API Key"""
        import secrets
        import string
        
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def hash_api_key(api_key):
        """对 API Key 进行哈希处理（用于存储）"""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    @staticmethod
    def validate_api_key_format(api_key):
        """验证 API Key 格式"""
        if not api_key:
            return False, "API Key 不能为空"
        
        if len(api_key) < 16:
            return False, "API Key 长度至少为 16 个字符"
        
        if len(api_key) > 128:
            return False, "API Key 长度不能超过 128 个字符"
        
        # 检查是否只包含字母数字字符
        if not api_key.replace('-', '').replace('_', '').isalnum():
            return False, "API Key 只能包含字母、数字、连字符和下划线"
        
        return True, "API Key 格式有效"
