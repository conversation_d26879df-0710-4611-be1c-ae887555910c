from flask import current_app
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.flask import VannaFlaskApp
from openai import OpenAI
import pandas as pd
import logging
import uuid
import os

logger = logging.getLogger(__name__)

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, client=None, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=client, config=config)


class VannaMysqlClient:
    def train(self, ddl=None, documentation=None, sql=None, question_sql_pairs=None):
        """训练系统"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        
        # 如果 ddl, documentation, sql, question_sql_pairs 都为 None 时处理 plan
        if ddl or documentation or sql or question_sql_pairs:
            if ddl:
                self.vn.train(ddl=ddl)
            if documentation:
                self.vn.train(documentation=documentation)
            if sql:
                self.vn.train(sql=sql)
            if question_sql_pairs:
                for pair in question_sql_pairs:
                    self.vn.train(question=pair['question'], sql=pair['sql'])
        else:
            df_information_schema = self.vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
            plan = self.vn.get_training_plan_generic(df_information_schema)
            self.vn.train(plan=plan)
    def ask(self, question):
        """根据问题生成 SQL 查询并返回结果"""
        import logging
        logger = logging.getLogger(__name__)

        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")

        if not question or not isinstance(question, str):
            raise ValueError("问题不能为空且必须是字符串")

        logger.info(f"开始处理问题: {question}")

        # 生成 SQL
        try:
            logger.info("正在生成 SQL...")
            sql = self.vn.generate_sql(question=question)
            logger.info(f"生成的 SQL: {sql}")
            logger.info(f"SQL 类型: {type(sql)}")

            if not sql:
                raise ValueError("无法生成 SQL 查询，请检查问题描述或数据库连接")
        except Exception as e:
            logger.error(f"SQL 生成失败: {str(e)}")
            raise ValueError(f"SQL 生成失败: {str(e)}")

        # 执行 SQL
        try:
            logger.info("正在执行 SQL...")
            df = self.vn.run_sql(sql)
            logger.info(f"SQL 执行结果类型: {type(df)}")

            if df is not None:
                logger.info(f"查询结果形状: {df.shape}")
            else:
                logger.warning("SQL 执行返回 None")
                raise ValueError("SQL 执行失败，请检查生成的 SQL 语句")
        except Exception as e:
            logger.error(f"SQL 执行失败: {str(e)}")
            raise ValueError(f"SQL 执行失败: {str(e)}")

        
        # 生成 CSV 文件
        csvfilepath = None
        try:
            csvfilepath = self.generate_unique_filename(extension="csv")
            df.to_csv(csvfilepath, index=False, sep=",", encoding='utf-8')
        except Exception as e:
            print(f"生成 CSV 文件时出错: {str(e)}")
            csvfilepath = None

        # 构建返回结果
        result = {
            "sql": sql,
            "csvfilename": os.path.basename(csvfilepath) if csvfilepath else None,
            "query_results": df.to_json(orient='records') if df is not None and not df.empty else None,
            "row_count": len(df) if df is not None else 0
        }

        return result
    def generate_unique_filename(self, extension="png"):
        unique_id = uuid.uuid4()
        directory = f"./data/{extension}/"
        os.makedirs(directory, exist_ok=True)
        return f"{directory}{unique_id}.{extension}"
        
    def __init__(self):
        self.vn = None
        self.initialized = False

    def initialize(self, config=None):
        """初始化Vanna客户端"""
        logger.info(f"Vanna客户端初始化配置: {config}")
        try:
            if config is None:
                # 从Flask应用上下文获取配置
                api_key = current_app.config.get('OPEN_API_KEY')
                model_name = current_app.config.get('OPEN_MODEL_NAME')
                base_url = current_app.config.get('OPEN_BASE_URL')
                mysql_config = current_app.config.get('MYSQL_CONFIG')
            else:
                # 使用传入的配置参数
                api_key = config.get('OPEN_API_KEY')
                model_name = config.get('OPEN_MODEL_NAME')
                base_url = config.get('OPEN_BASE_URL')
                mysql_config = config.get('MYSQL_CONFIG')

            if not api_key:
                raise ValueError("VANNA_API_KEY未配置")

            # 初始化Vanna客户端
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
            )

            self.vn = MyVanna(client=client, config={"model": model_name, "path": "chromadb"})
            logger.info(f"MySQL配置: {mysql_config}")

            self.vn.connect_to_mysql(
                host=mysql_config['host'],
                dbname=mysql_config['database'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                port=mysql_config['port']
            )

            self.initialized = True
            logger.info("Vanna客户端初始化成功")
            return True

        except Exception as e:
            logger.error(f"Vanna客户端初始化失败: {str(e)}")
            self.initialized = False
            return False