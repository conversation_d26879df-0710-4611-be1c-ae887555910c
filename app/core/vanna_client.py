from flask import current_app
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.flask import VannaFlaskApp
from openai import OpenAI
import pandas as pd
import logging
import uuid
import os

logger = logging.getLogger(__name__)

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, client=None, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=client, config=config)


class VannaMysqlClient:
    def train(self, ddl=None, documentation=None, sql=None, question_sql_pairs=None):
        """训练系统"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        
        # 如果 ddl, documentation, sql, question_sql_pairs 都为 None 时处理 plan
        if ddl or documentation or sql or question_sql_pairs:
            if ddl:
                self.vn.train(ddl=ddl)
            if documentation:
                self.vn.train(documentation=documentation)
            if sql:
                self.vn.train(sql=sql)
            if question_sql_pairs:
                for pair in question_sql_pairs:
                    self.vn.train(question=pair['question'], sql=pair['sql'])
        else:
            df_information_schema = self.vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
            plan = self.vn.get_training_plan_generic(df_information_schema)
            self.vn.train(plan=plan)
    def ask(self, question):
        """根据问题生成 SQL 查询并返回结果"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        #sql, df, fig = self.vn.ask(question, print_results=False, visualize=True)

        sql = self.vn.generate_sql(question=question)
        df = self.vn.run_sql(sql)

        plotly_code = self.vn.generate_plotly_code(
            question=question,
            sql=sql,
            df_metadata=f"Running df.dtypes gives:\n {df.dtypes}",
        )
        #fig = self.vn.get_plotly_figure(plotly_code=plotly_code, df=df)
        #imagefilpath = self.generate_unique_filename(extension="png")
        csvfilpath = self.generate_unique_filename(extension="csv")
        #fig.write_image(imagefilpath)
        df.to_csv(csvfilpath, index=False, sep=",", encoding='utf-8')
        
        result = {
            "sql": sql,
            #"imagefilename": os.path.basename(imagefilpath),
            "csvfilename": os.path.basename(csvfilpath),
            "query_results": df.to_json(orient='records') if df is not None else None
        }
        
        return result
    def generate_unique_filename(self, extension="png"):
        unique_id = uuid.uuid4()
        directory = f"./data/{extension}/"
        os.makedirs(directory, exist_ok=True)
        return f"{directory}{unique_id}.{extension}"
        
    def __init__(self):
        self.vn = None
        self.initialized = False

    def initialize(self, config=None):
        """初始化Vanna客户端"""
        try:
            if config is None:
                # 从Flask应用上下文获取配置
                api_key = current_app.config.get('OPEN_API_KEY')
                model_name = current_app.config.get('OPEN_MODEL_NAME')
                base_url = current_app.config.get('OPEN_BASE_URL')
                mysql_config = current_app.config.get('MYSQL_CONFIG')
            else:
                # 使用传入的配置参数
                api_key = config.get('OPEN_API_KEY')
                model_name = config.get('OPEN_MODEL_NAME')
                base_url = config.get('OPEN_BASE_URL')
                mysql_config = config.get('MYSQL_CONFIG')

            if not api_key:
                raise ValueError("VANNA_API_KEY未配置")

            # 初始化Vanna客户端
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
            )

            self.vn = MyVanna(client=client, config={"model": model_name, "path": "chromadb"})

            self.vn.connect_to_mysql(
                host=mysql_config['host'],
                dbname=mysql_config['database'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                port=mysql_config['port']
            )

            self.initialized = True
            logger.info("Vanna客户端初始化成功")
            return True

        except Exception as e:
            logger.error(f"Vanna客户端初始化失败: {str(e)}")
            self.initialized = False
            return False