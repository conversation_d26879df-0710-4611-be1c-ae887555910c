from flask import current_app
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.flask import VannaFlaskApp
from openai import OpenAI
import pandas as pd
import logging

logger = logging.getLogger(__name__)

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, client=None, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=client, config=config)


class VannaMysqlClient:
    def train(self, ddl=None, documentation=None, sql=None, question_sql_pairs=None):
        """训练系统"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        
        # 如果 ddl, documentation, sql, question_sql_pairs 都为 None 时处理 plan
        if ddl or documentation or sql or question_sql_pairs:
            if ddl:
                self.vn.train(ddl=ddl)
            if documentation:
                self.vn.train(documentation=documentation)
            if sql:
                self.vn.train(sql=sql)
            if question_sql_pairs:
                for pair in question_sql_pairs:
                    self.vn.train(question=pair['question'], sql=pair['sql'])
        else:
            df_information_schema = self.vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
            plan = self.vn.get_training_plan_generic(df_information_schema)
            self.vn.train(plan=plan)
    def ask(self, question):
        """根据问题生成 SQL 查询并返回结果"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        sql, df, fig = self.vn.ask(question, print_results=False)
        if sql:
            print("Generated SQL:", sql)
        if df is not None:
            print("Query Results:")
            print(df.to_csv(sep='\t', na_rep='nan'))
        if fig:
            # 处理图形结果
            pass

    def __init__(self):
        self.vn = None
        self.initialized = False

    def initialize(self):
        """初始化Vanna客户端"""
        try:
            api_key = current_app.config.get('OPEN_API_KEY')
            model_name = current_app.config.get('OPEN_MODEL_NAME')
            base_url = current_app.config.get('OPEN_BASE_URL')
            mysql_config = current_app.config.get('MYSQL_CONFIG')

            if not api_key:
                raise ValueError("VANNA_API_KEY未配置")

            # 初始化Vanna客户端
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
            )

            self.vn = MyVanna(client=client, config={"model": model_name})

            self.vn.connect_to_mysql(
                host=mysql_config['host'],
                dbname=mysql_config['database'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                port=mysql_config['port']
            )

            self.initialized = True
            logger.info("Vanna客户端初始化成功")
            return True

        except Exception as e:
            logger.error(f"Vanna客户端初始化失败: {str(e)}")
            self.initialized = False
            return False