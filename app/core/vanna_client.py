from flask import current_app
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.flask import VannaFlaskApp
from openai import OpenAI
import pandas as pd
import logging
import uuid
import os

logger = logging.getLogger(__name__)


def _mask_sensitive_info(config):
    """屏蔽配置中的敏感信息用于日志记录"""
    if not isinstance(config, dict):
        return config

    masked_config = config.copy()
    sensitive_keys = ['password', 'api_key', 'secret_key', 'token', 'key']

    for key, value in masked_config.items():
        if isinstance(key, str):
            key_lower = key.lower()
            # 检查是否包含敏感关键词
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                if value:
                    # 保留前3个字符和后2个字符，中间用*替代
                    if len(str(value)) > 5:
                        masked_config[key] = f"{str(value)[:3]}***{str(value)[-2:]}"
                    else:
                        masked_config[key] = "***"
                else:
                    masked_config[key] = None
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                masked_config[key] = _mask_sensitive_info(value)

    return masked_config

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, client=None, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=client, config=config)


class VannaMysqlClient:
    def train(self, ddl=None, documentation=None, sql=None, question_sql_pairs=None):
        """训练系统"""
        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")
        
        # 如果 ddl, documentation, sql, question_sql_pairs 都为 None 时处理 plan
        if ddl or documentation or sql or question_sql_pairs:
            if ddl:
                self.vn.train(ddl=ddl)
            if documentation:
                self.vn.train(documentation=documentation)
            if sql:
                self.vn.train(sql=sql)
            if question_sql_pairs:
                for pair in question_sql_pairs:
                    self.vn.train(question=pair['question'], sql=pair['sql'])
        else:
            df_information_schema = self.vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
            plan = self.vn.get_training_plan_generic(df_information_schema)
            self.vn.train(plan=plan)
    def get_training_data(self):
        """获取训练数据列表"""
        import logging
        logger = logging.getLogger(__name__)

        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")

        try:
            logger.info("获取训练数据列表...")
            training_data = self.vn.get_training_data()

            # 处理 DataFrame 对象
            if training_data is not None:
                if hasattr(training_data, 'empty'):
                    # 如果是 DataFrame
                    if training_data.empty:
                        logger.info("获取到 0 条训练数据")
                        return []
                    else:
                        # 将 DataFrame 转换为字典列表
                        data_list = training_data.to_dict('records')
                        logger.info(f"获取到 {len(data_list)} 条训练数据")
                        return data_list
                elif isinstance(training_data, list):
                    # 如果已经是列表
                    logger.info(f"获取到 {len(training_data)} 条训练数据")
                    return training_data
                else:
                    # 其他类型，尝试转换为列表
                    logger.info(f"获取到训练数据，类型: {type(training_data)}")
                    return [training_data] if training_data else []
            else:
                logger.info("获取到 0 条训练数据")
                return []

        except Exception as e:
            logger.error(f"获取训练数据失败: {str(e)}")
            raise ValueError(f"获取训练数据失败: {str(e)}")

    def remove_training_data(self, id=None):
        """删除训练数据"""
        import logging
        logger = logging.getLogger(__name__)

        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")

        if not id:
            raise ValueError("删除训练数据需要提供 ID")

        try:
            logger.info(f"删除训练数据: {id}")
            result = self.vn.remove_training_data(id=id)
            logger.info(f"训练数据删除成功: {id}")
            return result
        except Exception as e:
            logger.error(f"删除训练数据失败: {str(e)}")
            raise ValueError(f"删除训练数据失败: {str(e)}")

    def ask(self, question):
        """根据问题生成 SQL 查询并返回结果"""
        import logging
        logger = logging.getLogger(__name__)

        if not self.initialized:
            raise RuntimeError("Vanna客户端未初始化")

        if not question or not isinstance(question, str):
            raise ValueError("问题不能为空且必须是字符串")

        logger.info(f"开始处理问题: {question}")

        # 生成 SQL
        try:
            logger.info("正在生成 SQL...")
            sql = self.vn.generate_sql(question=question)
            logger.info(f"生成的 SQL: {sql}")
            logger.info(f"SQL 类型: {type(sql)}")

            if not sql:
                raise ValueError("无法生成 SQL 查询，请检查问题描述或数据库连接")
        except Exception as e:
            logger.error(f"SQL 生成失败: {str(e)}")
            raise ValueError(f"SQL 生成失败: {str(e)}")

        # 执行 SQL
        try:
            logger.info("正在执行 SQL...")
            df = self.vn.run_sql(sql)
            logger.info(f"SQL 执行结果类型: {type(df)}")

            if df is not None:
                logger.info(f"查询结果形状: {df.shape}")
            else:
                logger.warning("SQL 执行返回 None")
                raise ValueError("SQL 执行失败，请检查生成的 SQL 语句")
        except Exception as e:
            logger.error(f"SQL 执行失败: {str(e)}")
            raise ValueError(f"SQL 执行失败: {str(e)}")

        
        # 生成 CSV 文件
        csvfilepath = None
        try:
            csvfilepath = self.generate_unique_filename(extension="csv")
            df.to_csv(csvfilepath, index=False, sep=",", encoding='utf-8')
        except Exception as e:
            print(f"生成 CSV 文件时出错: {str(e)}")
            csvfilepath = None

        # 构建返回结果
        result = {
            "sql": sql,
            "csvfilename": os.path.basename(csvfilepath) if csvfilepath else None,
            "query_results": df.to_json(orient='records') if df is not None and not df.empty else None,
            "row_count": len(df) if df is not None else 0
        }

        return result
    def generate_unique_filename(self, extension="png"):
        unique_id = uuid.uuid4()
        directory = f"./data/{extension}/"
        os.makedirs(directory, exist_ok=True)
        return f"{directory}{unique_id}.{extension}"
        
    def __init__(self):
        self.vn = None
        self.initialized = False

    def initialize(self, config=None):
        """初始化Vanna客户端"""
        logger.info(f"Vanna客户端初始化配置: {_mask_sensitive_info(config)}")
        try:
            if config is None:
                # 从Flask应用上下文获取配置
                api_key = current_app.config.get('VANNA_OPEN_API_KEY')
                model_name = current_app.config.get('VANNA_OPEN_MODEL_NAME')
                base_url = current_app.config.get('VANNA_OPEN_BASE_URL')
                mysql_config = current_app.config.get('VANNA_MYSQL_CONFIG')
            else:
                # 使用传入的配置参数
                api_key = config.get('VANNA_OPEN_API_KEY')
                model_name = config.get('VANNA_OPEN_MODEL_NAME')
                base_url = config.get('VANNA_OPEN_BASE_URL')
                mysql_config = config.get('VANNA_MYSQL_CONFIG')

            if not api_key:
                raise ValueError("VANNA_API_KEY未配置")

            # 初始化Vanna客户端
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
            )

            self.vn = MyVanna(client=client, config={"model": model_name, "path": "chromadb"})
            logger.info(f"MySQL配置: {_mask_sensitive_info(mysql_config)}")

            self.vn.connect_to_mysql(
                host=mysql_config['host'],
                dbname=mysql_config['database'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                port=mysql_config['port']
            )

            self.initialized = True
            logger.info("Vanna客户端初始化成功")
            return True

        except Exception as e:
            logger.error(f"Vanna客户端初始化失败: {str(e)}")
            self.initialized = False
            return False