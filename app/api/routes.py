from flask import Blueprint, request, current_app, send_file, url_for
from app.core.vanna_client import VannaMysqlClient
from app.auth import require_api_key, optional_api_key, APIKeyManager

from app.utils.response import success_response, error_response
import logging
import threading
import os
import mimetypes
from datetime import datetime

# Ensure logger is defined
logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# 全局实例
vanna_client = VannaMysqlClient()


def _build_mysql_config(request_data):
    """构建 MySQL 配置，优先使用请求参数，否则使用默认配置"""
    default_config = current_app.config.get('MYSQL_CONFIG', {})

    # 从请求中提取 MySQL 配置参数
    mysql_params = {}
    mysql_fields = ['host', 'port', 'user', 'password', 'database']

    for field in mysql_fields:
        # 检查请求中是否有 mysql_ 前缀的参数
        mysql_key = f'mysql_{field}'
        if mysql_key in request_data:
            mysql_params[field] = request_data[mysql_key]
        # 也支持直接使用字段名
        elif field in request_data:
            mysql_params[field] = request_data[field]

    # 合并配置：请求参数优先，默认配置作为后备
    final_config = default_config.copy()
    final_config.update(mysql_params)

    # 确保 port 是整数
    if 'port' in final_config:
        try:
            final_config['port'] = int(final_config['port'])
        except (ValueError, TypeError):
            final_config['port'] = 3306

    return final_config


def _validate_mysql_config(mysql_config):
    """验证 MySQL 配置参数"""
    required_fields = ['host', 'user', 'database']
    missing_fields = []

    for field in required_fields:
        if not mysql_config.get(field):
            missing_fields.append(field)

    if missing_fields:
        return False, f"缺少必需的 MySQL 配置参数: {', '.join(missing_fields)}"

    # 验证端口号
    port = mysql_config.get('port', 3306)
    if not isinstance(port, int) or port < 1 or port > 65535:
        return False, "MySQL 端口号必须是 1-65535 之间的整数"

    return True, "MySQL 配置验证通过"


def _train_async(ddl, documentation, sql, question_sql_pairs, config):
    """异步训练函数"""
    try:
        logger.info("开始异步训练...")
        vanna_client.initialize(config=config)
        vanna_client.train(ddl=ddl, documentation=documentation, sql=sql, question_sql_pairs=question_sql_pairs)
        logger.info("异步训练完成")
    except Exception as e:
        logger.error(f"异步训练错误: {str(e)}")


@api_bp.route('/train', methods=['POST'])
@require_api_key
def train():
    """训练系统

    支持的参数：
    - ddl: DDL 语句
    - documentation: 文档说明
    - sql: SQL 语句
    - question_sql_pairs: 问题-SQL 对列表
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("请求参数错误", "INVALID_PARAMS")

        # 提取训练相关参数
        ddl = data.get('ddl')
        documentation = data.get('documentation')
        sql = data.get('sql')
        question_sql_pairs = data.get('question_sql_pairs')

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 在主线程中获取配置，避免在异步线程中访问Flask上下文
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 在后台线程中异步执行训练
        train_thread = threading.Thread(
            target=_train_async,
            args=(ddl, documentation, sql, question_sql_pairs, config),
            daemon=True
        )
        train_thread.start()

        # 返回使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'mysql_config': mysql_info,
            'message': '训练已开始，正在后台执行'
        }, "训练启动成功")

    except Exception as e:
        logger.error(f"训练启动错误: {str(e)}")
        return error_response(f"训练启动失败: {str(e)}", "TRAIN_START_ERROR", 500)

@api_bp.route('/ask', methods=['POST'])
@require_api_key
def ask():
    """根据问题生成 SQL 查询并返回结果

    支持的参数：
    - question: 要查询的问题 (必需)
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return error_response("请求参数错误，缺少question字段", "INVALID_PARAMS")

        question = data['question'].strip()
        if not question:
            return error_response("问题不能为空", "EMPTY_QUESTION")

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 构建完整配置
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 初始化客户端并执行查询
        vanna_client.initialize(config=config)
        result = vanna_client.ask(question)

        # 返回结果和使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'result': result,
            'mysql_config': mysql_info,
            'question': question
        }, "查询成功")

    except Exception as e:
        logger.error(f"查询错误: {str(e)}")
        return error_response(f"查询失败: {str(e)}", "ASK_ERROR", 500)


@api_bp.route('/mysql/test-connection', methods=['POST'])
@require_api_key
def test_mysql_connection():
    """测试 MySQL 连接

    支持的参数：
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json() or {}

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 测试连接
        connection_result = _test_mysql_connection(mysql_config)

        # 返回测试结果和使用的配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        if connection_result['success']:
            return success_response({
                'connection_status': 'success',
                'mysql_config': mysql_info,
                'server_info': connection_result.get('server_info'),
                'database_exists': connection_result.get('database_exists'),
                'response_time_ms': connection_result.get('response_time_ms')
            }, "MySQL 连接测试成功")
        else:
            return error_response(
                f"MySQL 连接失败: {connection_result.get('error', '未知错误')}",
                "MYSQL_CONNECTION_FAILED",
                400
            )

    except Exception as e:
        logger.error(f"MySQL 连接测试错误: {str(e)}")
        return error_response(f"连接测试失败: {str(e)}", "CONNECTION_TEST_ERROR", 500)


def _test_mysql_connection(mysql_config):
    """测试 MySQL 连接的内部函数"""
    import pymysql
    import time

    connection = None
    start_time = time.time()

    try:
        # 尝试连接到 MySQL 服务器
        connection = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config.get('password', ''),
            charset='utf8mb4',
            connect_timeout=10,
            read_timeout=10,
            write_timeout=10
        )

        # 计算响应时间
        response_time = (time.time() - start_time) * 1000

        # 获取服务器信息
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            server_version = cursor.fetchone()[0]

            # 检查数据库是否存在
            database_exists = False
            if mysql_config.get('database'):
                cursor.execute("SHOW DATABASES LIKE %s", (mysql_config['database'],))
                database_exists = cursor.fetchone() is not None

                # 如果数据库存在，尝试切换到该数据库
                if database_exists:
                    cursor.execute(f"USE `{mysql_config['database']}`")

        return {
            'success': True,
            'server_info': {
                'version': server_version,
                'host': mysql_config['host'],
                'port': mysql_config['port']
            },
            'database_exists': database_exists,
            'response_time_ms': round(response_time, 2)
        }

    except pymysql.Error as e:
        error_code = getattr(e, 'args', [None])[0] if hasattr(e, 'args') and e.args else None
        error_msg = str(e)

        # 根据错误代码提供更友好的错误信息
        if error_code == 1045:
            error_msg = "用户名或密码错误"
        elif error_code == 1049:
            error_msg = f"数据库 '{mysql_config.get('database')}' 不存在"
        elif error_code == 2003:
            error_msg = f"无法连接到 MySQL 服务器 '{mysql_config['host']}:{mysql_config['port']}'"
        elif error_code == 1044:
            error_msg = f"用户 '{mysql_config['user']}' 没有访问数据库的权限"

        return {
            'success': False,
            'error': error_msg,
            'error_code': error_code,
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }

    except Exception as e:
        return {
            'success': False,
            'error': f"连接测试异常: {str(e)}",
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }

    finally:
        if connection:
            connection.close()


@api_bp.route('/auth/generate-key', methods=['POST'])
@require_api_key
def generate_api_key():
    """生成新的 API Key（需要现有有效的 API Key）"""
    try:
        data = request.get_json() or {}
        length = data.get('length', 32)

        if length < 16 or length > 128:
            return error_response("API Key 长度必须在 16-128 之间", "INVALID_LENGTH")

        new_key = APIKeyManager.generate_api_key(length)

        return success_response({
            'api_key': new_key,
            'length': len(new_key),
            'note': '请妥善保存此 API Key，它不会再次显示'
        }, "API Key 生成成功")

    except Exception as e:
        logger.error(f"生成 API Key 错误: {str(e)}")
        return error_response(f"生成失败: {str(e)}", "GENERATE_KEY_ERROR", 500)


@api_bp.route('/auth/validate-key', methods=['POST'])
def validate_api_key():
    """验证 API Key 格式和有效性"""
    try:
        data = request.get_json()
        if not data or 'api_key' not in data:
            return error_response("请求参数错误，缺少 api_key 字段", "INVALID_PARAMS")

        api_key = data['api_key']

        # 验证格式
        is_valid_format, format_message = APIKeyManager.validate_api_key_format(api_key)
        if not is_valid_format:
            return error_response(format_message, "INVALID_FORMAT")

        # 验证是否在有效列表中
        from app.auth.api_key_auth import APIKeyAuth
        is_authorized = APIKeyAuth.validate_api_key(api_key)

        return success_response({
            'format_valid': is_valid_format,
            'authorized': is_authorized,
            'message': '有效的 API Key' if is_authorized else '格式正确但未授权'
        }, "验证完成")

    except Exception as e:
        logger.error(f"验证 API Key 错误: {str(e)}")
        return error_response(f"验证失败: {str(e)}", "VALIDATE_KEY_ERROR", 500)


@api_bp.route('/auth/info', methods=['GET'])
@optional_api_key
def auth_info():
    """获取认证信息（可选认证）"""
    try:
        api_key = getattr(request, 'api_key', None)
        auth_enabled = current_app.config.get('ENABLE_API_KEY_AUTH', True)

        info = {
            'auth_enabled': auth_enabled,
            'authenticated': api_key is not None,
            'api_key_provided': api_key is not None,
            'supported_headers': ['Authorization: Bearer <key>', 'X-API-Key: <key>', 'API-Key: <key>']
        }

        if api_key:
            info['api_key_preview'] = f"{api_key[:8]}..." if len(api_key) > 8 else api_key

        return success_response(info, "认证信息获取成功")

    except Exception as e:
        logger.error(f"获取认证信息错误: {str(e)}")
        return error_response(f"获取失败: {str(e)}", "AUTH_INFO_ERROR", 500)


@api_bp.route('/files/list', methods=['GET'])
@require_api_key
def list_files():
    """获取 data 目录中的文件列表

    返回 CSV 文件和图片文件的列表，包含下载链接
    """
    try:
        # 获取 data 目录路径
        data_dir = os.path.join(current_app.root_path, '..', 'data')
        data_dir = os.path.abspath(data_dir)

        if not os.path.exists(data_dir):
            # 如果 data 目录不存在，创建它
            os.makedirs(data_dir, exist_ok=True)
            logger.info(f"创建 data 目录: {data_dir}")

        # 支持的文件类型
        csv_extensions = {'.csv'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}

        csv_files = []
        image_files = []

        # 遍历 data 目录
        for filename in os.listdir(data_dir):
            file_path = os.path.join(data_dir, filename)

            # 跳过目录
            if os.path.isdir(file_path):
                continue

            # 获取文件信息
            file_ext = os.path.splitext(filename)[1].lower()
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            file_mtime = datetime.fromtimestamp(file_stat.st_mtime)

            file_info = {
                'filename': filename,
                'size': file_size,
                'size_human': _format_file_size(file_size),
                'modified_time': file_mtime.isoformat(),
                'download_url': f"/api/v1/files/download/{filename}",
                'extension': file_ext
            }

            # 分类文件
            if file_ext in csv_extensions:
                csv_files.append(file_info)
            elif file_ext in image_extensions:
                # 为图片添加预览 URL
                file_info['preview_url'] = f"/api/v1/files/preview/{filename}"
                image_files.append(file_info)

        # 按修改时间排序（最新的在前）
        csv_files.sort(key=lambda x: x['modified_time'], reverse=True)
        image_files.sort(key=lambda x: x['modified_time'], reverse=True)

        return success_response({
            'data_directory': data_dir,
            'csv_files': csv_files,
            'image_files': image_files,
            'total_files': len(csv_files) + len(image_files),
            'csv_count': len(csv_files),
            'image_count': len(image_files)
        }, "文件列表获取成功")

    except Exception as e:
        logger.error(f"获取文件列表错误: {str(e)}")
        return error_response(f"获取文件列表失败: {str(e)}", "LIST_FILES_ERROR", 500)


@api_bp.route('/files/download/<filename>', methods=['GET'])
@require_api_key
def download_file(filename):
    """下载 data 目录中的文件"""
    try:
        # 安全检查：防止路径遍历攻击
        if '..' in filename or '/' in filename or '\\' in filename:
            return error_response("无效的文件名", "INVALID_FILENAME", 400)

        # 获取文件路径
        data_dir = os.path.join(current_app.root_path, '..', 'data')
        data_dir = os.path.abspath(data_dir)
        file_path = os.path.join(data_dir, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return error_response("文件不存在", "FILE_NOT_FOUND", 404)

        # 检查是否为文件（不是目录）
        if not os.path.isfile(file_path):
            return error_response("不是有效的文件", "INVALID_FILE", 400)

        # 获取 MIME 类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'

        # 记录下载日志
        logger.info(f"文件下载: {filename}, 大小: {os.path.getsize(file_path)} bytes")

        # 发送文件
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype=mime_type
        )

    except Exception as e:
        logger.error(f"文件下载错误: {str(e)}")
        return error_response(f"文件下载失败: {str(e)}", "DOWNLOAD_ERROR", 500)


@api_bp.route('/files/preview/<filename>', methods=['GET'])
@require_api_key
def preview_file(filename):
    """预览文件（主要用于图片）"""
    try:
        # 安全检查：防止路径遍历攻击
        if '..' in filename or '/' in filename or '\\' in filename:
            return error_response("无效的文件名", "INVALID_FILENAME", 400)

        # 获取文件路径
        data_dir = os.path.join(current_app.root_path, '..', 'data')
        data_dir = os.path.abspath(data_dir)
        file_path = os.path.join(data_dir, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return error_response("文件不存在", "FILE_NOT_FOUND", 404)

        # 检查是否为文件（不是目录）
        if not os.path.isfile(file_path):
            return error_response("不是有效的文件", "INVALID_FILE", 400)

        # 获取 MIME 类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'

        # 记录预览日志
        logger.info(f"文件预览: {filename}")

        # 发送文件（不作为附件，浏览器会尝试显示）
        return send_file(
            file_path,
            as_attachment=False,
            mimetype=mime_type
        )

    except Exception as e:
        logger.error(f"文件预览错误: {str(e)}")
        return error_response(f"文件预览失败: {str(e)}", "PREVIEW_ERROR", 500)


def _format_file_size(size_bytes):
    """格式化文件大小为人类可读的格式"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"
