from flask import Blueprint, request, current_app, send_file, url_for
from app.core.vanna_client import VannaMysqlClient
from app.auth import require_api_key, optional_api_key, APIKeyManager

from app.utils.response import success_response, error_response
import logging
import threading
import os
import mimetypes
from datetime import datetime

# Ensure logger is defined
logger = logging.getLogger(__name__)


def _mask_sensitive_info(config):
    """屏蔽配置中的敏感信息用于日志记录"""
    if not isinstance(config, dict):
        return config

    masked_config = config.copy()
    sensitive_keys = ['password', 'api_key', 'secret_key', 'token', 'key']

    for key, value in masked_config.items():
        if isinstance(key, str):
            key_lower = key.lower()
            # 检查是否包含敏感关键词
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                if value:
                    # 保留前3个字符和后2个字符，中间用*替代
                    if len(str(value)) > 5:
                        masked_config[key] = f"{str(value)[:3]}***{str(value)[-2:]}"
                    else:
                        masked_config[key] = "***"
                else:
                    masked_config[key] = None
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                masked_config[key] = _mask_sensitive_info(value)

    return masked_config

api_bp = Blueprint('api', __name__)

# 全局实例
vanna_client = VannaMysqlClient()


def _build_mysql_config(request_data):
    """构建 MySQL 配置，优先使用请求参数，否则使用默认配置"""
    default_config = current_app.config.get('MYSQL_CONFIG', {})
    logger.debug("Default MySQL configuration: %s", _mask_sensitive_info(default_config))

    # 从请求中提取 MySQL 配置参数
    mysql_params = {}
    mysql_fields = ['host', 'port', 'user', 'password', 'database']

    for field in mysql_fields:
        # 检查请求中是否有 mysql_ 前缀的参数
        mysql_key = f'mysql_{field}'
        if mysql_key in request_data:
            mysql_params[field] = request_data[mysql_key]
        # 也支持直接使用字段名
        elif field in request_data:
            mysql_params[field] = request_data[field]

    # 合并配置：请求参数优先，默认配置作为后备
    final_config = default_config.copy()
    final_config.update(mysql_params)

    # 确保 port 是整数
    if 'port' in final_config:
        try:
            final_config['port'] = int(final_config['port'])
        except (ValueError, TypeError):
            final_config['port'] = 3306

    return final_config


def _validate_mysql_config(mysql_config):
    """验证 MySQL 配置参数"""
    required_fields = ['host', 'user', 'database']
    missing_fields = []

    for field in required_fields:
        if not mysql_config.get(field):
            missing_fields.append(field)

    if missing_fields:
        return False, f"缺少必需的 MySQL 配置参数: {', '.join(missing_fields)}"

    # 验证端口号
    port = mysql_config.get('port', 3306)
    if not isinstance(port, int) or port < 1 or port > 65535:
        return False, "MySQL 端口号必须是 1-65535 之间的整数"

    return True, "MySQL 配置验证通过"


def _train_async(ddl, documentation, sql, question_sql_pairs, config):
    """异步训练函数"""
    try:
        logger.info("开始异步训练...")
        logger.info(f"使用的配置: {_mask_sensitive_info(config)}")
        vanna_client.initialize(config=config)
        vanna_client.train(ddl=ddl, documentation=documentation, sql=sql, question_sql_pairs=question_sql_pairs)
        logger.info("异步训练完成")
    except Exception as e:
        logger.error(f"异步训练错误: {str(e)}")


@api_bp.route('/train', methods=['POST'])
@require_api_key
def train():
    """训练系统

    支持的参数：
    - ddl: DDL 语句
    - documentation: 文档说明
    - sql: SQL 语句
    - question_sql_pairs: 问题-SQL 对列表
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("请求参数错误", "INVALID_PARAMS")

        # 提取训练相关参数
        ddl = data.get('ddl')
        documentation = data.get('documentation')
        sql = data.get('sql')
        question_sql_pairs = data.get('question_sql_pairs')

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 在主线程中获取配置，避免在异步线程中访问Flask上下文
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 在后台线程中异步执行训练
        train_thread = threading.Thread(
            target=_train_async,
            args=(ddl, documentation, sql, question_sql_pairs, config),
            daemon=True
        )
        train_thread.start()

        # 返回使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'mysql_config': mysql_info,
            'message': '训练已开始，正在后台执行'
        }, "训练启动成功")

    except Exception as e:
        logger.error(f"训练启动错误: {str(e)}")
        return error_response(f"训练启动失败: {str(e)}", "TRAIN_START_ERROR", 500)

@api_bp.route('/ask', methods=['POST'])
@require_api_key
def ask():
    """根据问题生成 SQL 查询并返回结果

    支持的参数：
    - question: 要查询的问题 (必需)
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return error_response("请求参数错误，缺少question字段", "INVALID_PARAMS")

        question = data['question'].strip()
        if not question:
            return error_response("问题不能为空", "EMPTY_QUESTION")

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 构建完整配置
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 初始化客户端并执行查询
        vanna_client.initialize(config=config)

        # 添加详细的错误处理
        try:
            result = vanna_client.ask(question)
            if not result:
                raise ValueError("查询返回空结果")
        except ValueError as ve:
            logger.error(f"查询参数错误: {str(ve)}")
            return error_response(f"查询参数错误: {str(ve)}", "QUERY_PARAM_ERROR", 400)
        except RuntimeError as re:
            logger.error(f"查询运行时错误: {str(re)}")
            return error_response(f"查询运行时错误: {str(re)}", "QUERY_RUNTIME_ERROR", 500)
        except Exception as qe:
            logger.error(f"查询执行错误: {str(qe)}")
            return error_response(f"查询执行错误: {str(qe)}", "QUERY_EXECUTION_ERROR", 500)

        # 返回结果和使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'result': result,
            'mysql_config': mysql_info,
            'question': question
        }, "查询成功")

    except Exception as e:
        logger.error(f"查询错误: {str(e)}")
        return error_response(f"查询失败: {str(e)}", "ASK_ERROR", 500)


@api_bp.route('/mysql/test-connection', methods=['POST'])
@require_api_key
def test_mysql_connection():
    """测试 MySQL 连接

    支持的参数：
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json() or {}

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 测试连接
        connection_result = _test_mysql_connection(mysql_config)

        # 返回测试结果和使用的配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        if connection_result['success']:
            return success_response({
                'connection_status': 'success',
                'mysql_config': mysql_info,
                'server_info': connection_result.get('server_info'),
                'database_exists': connection_result.get('database_exists'),
                'response_time_ms': connection_result.get('response_time_ms')
            }, "MySQL 连接测试成功")
        else:
            return error_response(
                f"MySQL 连接失败: {connection_result.get('error', '未知错误')}",
                "MYSQL_CONNECTION_FAILED",
                400
            )

    except Exception as e:
        logger.error(f"MySQL 连接测试错误: {str(e)}")
        return error_response(f"连接测试失败: {str(e)}", "CONNECTION_TEST_ERROR", 500)


def _test_mysql_connection(mysql_config):
    """测试 MySQL 连接的内部函数"""
    import pymysql
    import time

    connection = None
    start_time = time.time()

    try:
        # 尝试连接到 MySQL 服务器
        connection = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config.get('password', ''),
            charset='utf8mb4',
            connect_timeout=10,
            read_timeout=10,
            write_timeout=10
        )

        # 计算响应时间
        response_time = (time.time() - start_time) * 1000

        # 获取服务器信息
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            server_version = cursor.fetchone()[0]

            # 检查数据库是否存在
            database_exists = False
            if mysql_config.get('database'):
                cursor.execute("SHOW DATABASES LIKE %s", (mysql_config['database'],))
                database_exists = cursor.fetchone() is not None

                # 如果数据库存在，尝试切换到该数据库
                if database_exists:
                    cursor.execute(f"USE `{mysql_config['database']}`")

        return {
            'success': True,
            'server_info': {
                'version': server_version,
                'host': mysql_config['host'],
                'port': mysql_config['port']
            },
            'database_exists': database_exists,
            'response_time_ms': round(response_time, 2)
        }

    except pymysql.Error as e:
        error_code = getattr(e, 'args', [None])[0] if hasattr(e, 'args') and e.args else None
        error_msg = str(e)

        # 根据错误代码提供更友好的错误信息
        if error_code == 1045:
            error_msg = "用户名或密码错误"
        elif error_code == 1049:
            error_msg = f"数据库 '{mysql_config.get('database')}' 不存在"
        elif error_code == 2003:
            error_msg = f"无法连接到 MySQL 服务器 '{mysql_config['host']}:{mysql_config['port']}'"
        elif error_code == 1044:
            error_msg = f"用户 '{mysql_config['user']}' 没有访问数据库的权限"

        return {
            'success': False,
            'error': error_msg,
            'error_code': error_code,
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }

    except Exception as e:
        return {
            'success': False,
            'error': f"连接测试异常: {str(e)}",
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }

    finally:
        if connection:
            connection.close()


@api_bp.route('/auth/generate-key', methods=['POST'])
@require_api_key
def generate_api_key():
    """生成新的 API Key（需要现有有效的 API Key）"""
    try:
        data = request.get_json() or {}
        length = data.get('length', 32)

        if length < 16 or length > 128:
            return error_response("API Key 长度必须在 16-128 之间", "INVALID_LENGTH")

        new_key = APIKeyManager.generate_api_key(length)

        return success_response({
            'api_key': new_key,
            'length': len(new_key),
            'note': '请妥善保存此 API Key，它不会再次显示'
        }, "API Key 生成成功")

    except Exception as e:
        logger.error(f"生成 API Key 错误: {str(e)}")
        return error_response(f"生成失败: {str(e)}", "GENERATE_KEY_ERROR", 500)


@api_bp.route('/auth/validate-key', methods=['POST'])
def validate_api_key():
    """验证 API Key 格式和有效性"""
    try:
        data = request.get_json()
        if not data or 'api_key' not in data:
            return error_response("请求参数错误，缺少 api_key 字段", "INVALID_PARAMS")

        api_key = data['api_key']

        # 验证格式
        is_valid_format, format_message = APIKeyManager.validate_api_key_format(api_key)
        if not is_valid_format:
            return error_response(format_message, "INVALID_FORMAT")

        # 验证是否在有效列表中
        from app.auth.api_key_auth import APIKeyAuth
        is_authorized = APIKeyAuth.validate_api_key(api_key)

        return success_response({
            'format_valid': is_valid_format,
            'authorized': is_authorized,
            'message': '有效的 API Key' if is_authorized else '格式正确但未授权'
        }, "验证完成")

    except Exception as e:
        logger.error(f"验证 API Key 错误: {str(e)}")
        return error_response(f"验证失败: {str(e)}", "VALIDATE_KEY_ERROR", 500)


@api_bp.route('/auth/info', methods=['GET'])
@optional_api_key
def auth_info():
    """获取认证信息（可选认证）"""
    try:
        api_key = getattr(request, 'api_key', None)
        auth_enabled = current_app.config.get('ENABLE_API_KEY_AUTH', True)

        info = {
            'auth_enabled': auth_enabled,
            'authenticated': api_key is not None,
            'api_key_provided': api_key is not None,
            'supported_headers': ['Authorization: Bearer <key>', 'X-API-Key: <key>', 'API-Key: <key>']
        }

        if api_key:
            # 安全地显示 API key 预览，避免暴露完整密钥
            if len(api_key) > 8:
                info['api_key_preview'] = f"{api_key[:4]}***{api_key[-2:]}"
            elif len(api_key) > 4:
                info['api_key_preview'] = f"{api_key[:2]}***"
            else:
                info['api_key_preview'] = "***"

        return success_response(info, "认证信息获取成功")

    except Exception as e:
        logger.error(f"获取认证信息错误: {str(e)}")
        return error_response(f"获取失败: {str(e)}", "AUTH_INFO_ERROR", 500)


@api_bp.route('/files', methods=['GET'])
def get_file():
    """统一文件接口：根据文件类型自动判断下载或显示

    - CSV 文件：自动下载
    - 图片文件：直接显示
    - 其他文件：根据 MIME 类型判断

    支持的目录结构：
    - data/csv/
    - data/png/ (或其他图片格式目录)
    - data/ (根目录)
    """
    filename = request.args.get('filename')
    try:
        # 安全检查：防止路径遍历攻击
        if '..' in filename or filename.startswith('/') or filename.startswith('\\'):
            return error_response("无效的文件名", "INVALID_FILENAME", 400)

        # 获取 data 目录路径
        data_root = os.path.join(current_app.root_path, '..', 'data')
        data_root = os.path.abspath(data_root)

        # 查找文件的可能路径
        file_path = _find_file_in_data_dirs(data_root, filename)

        if not file_path:
            return error_response("文件不存在", "FILE_NOT_FOUND", 404)

        # 检查是否为文件（不是目录）
        if not os.path.isfile(file_path):
            return error_response("不是有效的文件", "INVALID_FILE", 400)

        # 获取文件扩展名和 MIME 类型
        file_ext = os.path.splitext(filename)[1].lower()
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'

        # 根据文件类型决定处理方式
        csv_extensions = {'.csv'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}

        if file_ext in csv_extensions:
            # CSV 文件：强制下载
            logger.info(f"CSV 文件下载: {filename}, 大小: {os.path.getsize(file_path)} bytes")
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename,
                mimetype=mime_type
            )
        elif file_ext in image_extensions:
            # 图片文件：直接显示
            logger.info(f"图片文件显示: {filename}")
            return send_file(
                file_path,
                as_attachment=False,
                mimetype=mime_type
            )
        else:
            # 其他文件：根据 MIME 类型判断
            if mime_type.startswith('image/'):
                # 图片类型：直接显示
                logger.info(f"图片文件显示: {filename}")
                return send_file(
                    file_path,
                    as_attachment=False,
                    mimetype=mime_type
                )
            else:
                # 非图片类型：强制下载
                logger.info(f"文件下载: {filename}, 大小: {os.path.getsize(file_path)} bytes")
                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype=mime_type
                )

    except Exception as e:
        logger.error(f"文件处理错误: {str(e)}")
        return error_response(f"文件处理失败: {str(e)}", "FILE_ERROR", 500)


def _find_file_in_data_dirs(data_root, filename):
    """在 data 目录及其子目录中查找文件"""
    # 可能的搜索路径
    search_paths = [
        # 直接在 data 根目录
        os.path.join(data_root, filename),
        # 在 csv 子目录
        os.path.join(data_root, 'csv', filename),
        # 在 png 子目录
        os.path.join(data_root, 'png', filename),
        # 在 images 子目录
        os.path.join(data_root, 'images', filename),
        # 在 charts 子目录
        os.path.join(data_root, 'charts', filename),
    ]

    # 根据文件扩展名智能搜索
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext == '.csv':
        # CSV 文件优先在 csv 目录中查找
        search_paths = [
            os.path.join(data_root, 'csv', filename),
            os.path.join(data_root, filename),
        ]
    elif file_ext in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}:
        # 图片文件优先在图片相关目录中查找
        search_paths = [
            os.path.join(data_root, 'png', filename),
            os.path.join(data_root, 'images', filename),
            os.path.join(data_root, 'charts', filename),
            os.path.join(data_root, file_ext[1:], filename),  # 按扩展名的目录
            os.path.join(data_root, filename),
        ]

    # 查找文件
    for path in search_paths:
        if os.path.exists(path) and os.path.isfile(path):
            logger.debug(f"找到文件: {path}")
            return path

    return None



def _format_file_size(size_bytes):
    """格式化文件大小为人类可读的格式"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"
