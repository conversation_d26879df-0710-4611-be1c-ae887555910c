from flask import Blueprint, request
from app.core.vanna_client import VannaMysqlClient

from app.utils.response import success_response, error_response
import logging

# Ensure logger is defined
logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# 全局实例
vanna_client = VannaMysqlClient()


@api_bp.route('/train', methods=['POST'])
def train():
    """训练系统"""
    try:
        data = request.get_json()
        if not data:
            return error_response("请求参数错误", "INVALID_PARAMS")

        ddl = data.get('ddl')
        documentation = data.get('documentation')
        sql = data.get('sql')
        question_sql_pairs = data.get('question_sql_pairs')

        vanna_client.initialize()
        vanna_client.train(ddl=ddl, documentation=documentation, sql=sql, question_sql_pairs=question_sql_pairs)

        return success_response({}, "训练成功")

    except Exception as e:
        logger.error(f"训练错误: {str(e)}")
        return error_response(f"训练失败: {str(e)}", "TRAIN_ERROR", 500)

@api_bp.route('/ask', methods=['POST'])
def ask():
    """根据问题生成 SQL 查询并返回结果"""
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return error_response("请求参数错误，缺少question字段", "INVALID_PARAMS")

        question = data['question'].strip()
        if not question:
            return error_response("问题不能为空", "EMPTY_QUESTION")

        vanna_client.initialize()
        result = vanna_client.ask(question)

        return success_response(result, "查询成功")

    except Exception as e:
        logger.error(f"查询错误: {str(e)}")
        return error_response(f"查询失败: {str(e)}", "ASK_ERROR", 500)
