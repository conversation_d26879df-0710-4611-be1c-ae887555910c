from flask import Blueprint, request
from app.core.vanna_client import VannaMysqlClient

from app.utils.response import success_response, error_response
import logging
import threading

# Ensure logger is defined
logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# 全局实例
vanna_client = VannaMysqlClient()


def _train_async(ddl, documentation, sql, question_sql_pairs):
    """异步训练函数"""
    try:
        logger.info("开始异步训练...")
        vanna_client.initialize()
        vanna_client.train(ddl=ddl, documentation=documentation, sql=sql, question_sql_pairs=question_sql_pairs)
        logger.info("异步训练完成")
    except Exception as e:
        logger.error(f"异步训练错误: {str(e)}")


@api_bp.route('/train', methods=['POST'])
def train():
    """训练系统"""
    try:
        data = request.get_json()
        if not data:
            return error_response("请求参数错误", "INVALID_PARAMS")

        ddl = data.get('ddl')
        documentation = data.get('documentation')
        sql = data.get('sql')
        question_sql_pairs = data.get('question_sql_pairs')

        # 在后台线程中异步执行训练
        train_thread = threading.Thread(
            target=_train_async,
            args=(ddl, documentation, sql, question_sql_pairs),
            daemon=True
        )
        train_thread.start()

        return success_response({}, "训练已开始，正在后台执行")

    except Exception as e:
        logger.error(f"训练启动错误: {str(e)}")
        return error_response(f"训练启动失败: {str(e)}", "TRAIN_START_ERROR", 500)

@api_bp.route('/ask', methods=['POST'])
def ask():
    """根据问题生成 SQL 查询并返回结果"""
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return error_response("请求参数错误，缺少question字段", "INVALID_PARAMS")

        question = data['question'].strip()
        if not question:
            return error_response("问题不能为空", "EMPTY_QUESTION")

        vanna_client.initialize()
        result = vanna_client.ask(question)

        return success_response(result, "查询成功")

    except Exception as e:
        logger.error(f"查询错误: {str(e)}")
        return error_response(f"查询失败: {str(e)}", "ASK_ERROR", 500)
