from flask import Blueprint, request, current_app
from app.core.vanna_client import VannaMysqlClient
from app.auth import require_api_key, optional_api_key, APIKeyManager

from app.utils.response import success_response, error_response
import logging
import threading

# Ensure logger is defined
logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# 全局实例
vanna_client = VannaMysqlClient()


def _build_mysql_config(request_data):
    """构建 MySQL 配置，优先使用请求参数，否则使用默认配置"""
    default_config = current_app.config.get('MYSQL_CONFIG', {})

    # 从请求中提取 MySQL 配置参数
    mysql_params = {}
    mysql_fields = ['host', 'port', 'user', 'password', 'database']

    for field in mysql_fields:
        # 检查请求中是否有 mysql_ 前缀的参数
        mysql_key = f'mysql_{field}'
        if mysql_key in request_data:
            mysql_params[field] = request_data[mysql_key]
        # 也支持直接使用字段名
        elif field in request_data:
            mysql_params[field] = request_data[field]

    # 合并配置：请求参数优先，默认配置作为后备
    final_config = default_config.copy()
    final_config.update(mysql_params)

    # 确保 port 是整数
    if 'port' in final_config:
        try:
            final_config['port'] = int(final_config['port'])
        except (ValueError, TypeError):
            final_config['port'] = 3306

    return final_config


def _validate_mysql_config(mysql_config):
    """验证 MySQL 配置参数"""
    required_fields = ['host', 'user', 'database']
    missing_fields = []

    for field in required_fields:
        if not mysql_config.get(field):
            missing_fields.append(field)

    if missing_fields:
        return False, f"缺少必需的 MySQL 配置参数: {', '.join(missing_fields)}"

    # 验证端口号
    port = mysql_config.get('port', 3306)
    if not isinstance(port, int) or port < 1 or port > 65535:
        return False, "MySQL 端口号必须是 1-65535 之间的整数"

    return True, "MySQL 配置验证通过"


def _train_async(ddl, documentation, sql, question_sql_pairs, config):
    """异步训练函数"""
    try:
        logger.info("开始异步训练...")
        vanna_client.initialize(config=config)
        vanna_client.train(ddl=ddl, documentation=documentation, sql=sql, question_sql_pairs=question_sql_pairs)
        logger.info("异步训练完成")
    except Exception as e:
        logger.error(f"异步训练错误: {str(e)}")


@api_bp.route('/train', methods=['POST'])
@require_api_key
def train():
    """训练系统

    支持的参数：
    - ddl: DDL 语句
    - documentation: 文档说明
    - sql: SQL 语句
    - question_sql_pairs: 问题-SQL 对列表
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("请求参数错误", "INVALID_PARAMS")

        # 提取训练相关参数
        ddl = data.get('ddl')
        documentation = data.get('documentation')
        sql = data.get('sql')
        question_sql_pairs = data.get('question_sql_pairs')

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 在主线程中获取配置，避免在异步线程中访问Flask上下文
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 在后台线程中异步执行训练
        train_thread = threading.Thread(
            target=_train_async,
            args=(ddl, documentation, sql, question_sql_pairs, config),
            daemon=True
        )
        train_thread.start()

        # 返回使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'mysql_config': mysql_info,
            'message': '训练已开始，正在后台执行'
        }, "训练启动成功")

    except Exception as e:
        logger.error(f"训练启动错误: {str(e)}")
        return error_response(f"训练启动失败: {str(e)}", "TRAIN_START_ERROR", 500)

@api_bp.route('/ask', methods=['POST'])
@require_api_key
def ask():
    """根据问题生成 SQL 查询并返回结果

    支持的参数：
    - question: 要查询的问题 (必需)
    - mysql_host: MySQL 主机地址 (可选)
    - mysql_port: MySQL 端口 (可选)
    - mysql_user: MySQL 用户名 (可选)
    - mysql_password: MySQL 密码 (可选)
    - mysql_database: MySQL 数据库名 (可选)
    """
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return error_response("请求参数错误，缺少question字段", "INVALID_PARAMS")

        question = data['question'].strip()
        if not question:
            return error_response("问题不能为空", "EMPTY_QUESTION")

        # 构建 MySQL 配置
        mysql_config = _build_mysql_config(data)

        # 验证 MySQL 配置
        is_valid, error_msg = _validate_mysql_config(mysql_config)
        if not is_valid:
            return error_response(error_msg, "INVALID_MYSQL_CONFIG")

        # 构建完整配置
        config = {
            'OPEN_API_KEY': current_app.config.get('OPEN_API_KEY'),
            'OPEN_MODEL_NAME': current_app.config.get('OPEN_MODEL_NAME'),
            'OPEN_BASE_URL': current_app.config.get('OPEN_BASE_URL'),
            'MYSQL_CONFIG': mysql_config
        }

        # 初始化客户端并执行查询
        vanna_client.initialize(config=config)
        result = vanna_client.ask(question)

        # 返回结果和使用的 MySQL 配置信息（不包含密码）
        mysql_info = {
            'host': mysql_config.get('host'),
            'port': mysql_config.get('port'),
            'user': mysql_config.get('user'),
            'database': mysql_config.get('database')
        }

        return success_response({
            'result': result,
            'mysql_config': mysql_info,
            'question': question
        }, "查询成功")

    except Exception as e:
        logger.error(f"查询错误: {str(e)}")
        return error_response(f"查询失败: {str(e)}", "ASK_ERROR", 500)


@api_bp.route('/auth/generate-key', methods=['POST'])
@require_api_key
def generate_api_key():
    """生成新的 API Key（需要现有有效的 API Key）"""
    try:
        data = request.get_json() or {}
        length = data.get('length', 32)

        if length < 16 or length > 128:
            return error_response("API Key 长度必须在 16-128 之间", "INVALID_LENGTH")

        new_key = APIKeyManager.generate_api_key(length)

        return success_response({
            'api_key': new_key,
            'length': len(new_key),
            'note': '请妥善保存此 API Key，它不会再次显示'
        }, "API Key 生成成功")

    except Exception as e:
        logger.error(f"生成 API Key 错误: {str(e)}")
        return error_response(f"生成失败: {str(e)}", "GENERATE_KEY_ERROR", 500)


@api_bp.route('/auth/validate-key', methods=['POST'])
def validate_api_key():
    """验证 API Key 格式和有效性"""
    try:
        data = request.get_json()
        if not data or 'api_key' not in data:
            return error_response("请求参数错误，缺少 api_key 字段", "INVALID_PARAMS")

        api_key = data['api_key']

        # 验证格式
        is_valid_format, format_message = APIKeyManager.validate_api_key_format(api_key)
        if not is_valid_format:
            return error_response(format_message, "INVALID_FORMAT")

        # 验证是否在有效列表中
        from app.auth.api_key_auth import APIKeyAuth
        is_authorized = APIKeyAuth.validate_api_key(api_key)

        return success_response({
            'format_valid': is_valid_format,
            'authorized': is_authorized,
            'message': '有效的 API Key' if is_authorized else '格式正确但未授权'
        }, "验证完成")

    except Exception as e:
        logger.error(f"验证 API Key 错误: {str(e)}")
        return error_response(f"验证失败: {str(e)}", "VALIDATE_KEY_ERROR", 500)


@api_bp.route('/auth/info', methods=['GET'])
@optional_api_key
def auth_info():
    """获取认证信息（可选认证）"""
    try:
        api_key = getattr(request, 'api_key', None)
        auth_enabled = current_app.config.get('ENABLE_API_KEY_AUTH', True)

        info = {
            'auth_enabled': auth_enabled,
            'authenticated': api_key is not None,
            'api_key_provided': api_key is not None,
            'supported_headers': ['Authorization: Bearer <key>', 'X-API-Key: <key>', 'API-Key: <key>']
        }

        if api_key:
            info['api_key_preview'] = f"{api_key[:8]}..." if len(api_key) > 8 else api_key

        return success_response(info, "认证信息获取成功")

    except Exception as e:
        logger.error(f"获取认证信息错误: {str(e)}")
        return error_response(f"获取失败: {str(e)}", "AUTH_INFO_ERROR", 500)
