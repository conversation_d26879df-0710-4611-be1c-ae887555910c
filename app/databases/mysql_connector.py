import pymysql
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class MySQLConnector:
    def __init__(self):
        self.connection = None

    def connect(self):
        """建立MySQL连接"""
        try:
            config = current_app.config['MYSQL_CONFIG']
            self.connection = pymysql.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info("MySQL连接成功")
            return True
        except Exception as e:
            logger.error(f"MySQL连接失败: {str(e)}")
            return False

    def execute_query(self, sql: str, params=None):
        """执行查询SQL"""
        if not self.connection or not self.connection.open:
            if not self.connect():
                raise Exception("无法连接到MySQL数据库")

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                result = cursor.fetchall()
                return result
        except Exception as e:
            logger.error(f"SQL执行失败: {str(e)}")
            raise e

    def test_connection(self):
        """测试数据库连接"""
        try:
            if not self.connect():
                return False

            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return False

    def get_table_schema(self):
        """获取数据库表结构信息"""
        try:
            schema_info = {}

            # 获取所有表名
            with self.connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()

                for table in tables:
                    table_name = list(table.values())[0]

                    # 获取表的列信息
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()

                    schema_info[table_name] = {
                        'columns': columns,
                        'sample_data': self._get_sample_data(table_name)
                    }

            return schema_info
        except Exception as e:
            logger.error(f"获取表结构失败: {str(e)}")
            return {}

    def _get_sample_data(self, table_name: str, limit: int = 3):
        """获取表的示例数据"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取示例数据失败: {str(e)}")
            return []

    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()
            logger.info("MySQL连接已关闭")