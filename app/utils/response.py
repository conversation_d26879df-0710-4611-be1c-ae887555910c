from flask import jsonify
from typing import Any, Dict, Optional

def success_response(data: Any = None, message: str = "Success") -> Dict:
    """成功响应格式"""
    response = {
        "success": True,
        "message": message,
        "data": data
    }
    return jsonify(response)

def error_response(message: str, error_code: str = "UNKNOWN_ERROR", status_code: int = 400) -> tuple:
    """错误响应格式"""
    response = {
        "success": False,
        "message": message,
        "error_code": error_code,
        "data": None
    }
    return jsonify(response), status_code