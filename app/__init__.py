from flask import Flask
from config import Config
import logging
import os
from logging.handlers import RotatingFileHandler


def configure_logging(app):
    """配置应用日志"""
    # 获取日志级别
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()

    # 确保日志目录存在
    log_dir = '/app/logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 配置文件日志处理器（带轮转）
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))

    # 配置错误日志处理器
    error_handler = RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)

    # 配置控制台日志处理器（用于开发和调试）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(getattr(logging, log_level, logging.INFO))

    # 配置 Flask 应用日志
    app.logger.setLevel(getattr(logging, log_level, logging.INFO))
    app.logger.addHandler(file_handler)
    app.logger.addHandler(error_handler)
    app.logger.addHandler(console_handler)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level, logging.INFO))

    # 清除现有处理器，避免重复
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)

    # 记录日志配置信息
    app.logger.info(f"日志系统已配置 - 级别: {log_level}, 目录: {log_dir}")


def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 配置日志
    configure_logging(app)


    # 注册路由
    from app.api.routes import api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')

    # 健康检查路由
    @app.route('/health')
    def health_check():
        return {'status': 'ok', 'message': 'Service is running'}

    # 简单的健康检查路由（只返回 ok）
    @app.route('/health/simple')
    def simple_health():
        return 'ok'

    return app