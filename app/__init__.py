from flask import Flask
from config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)


    # 注册路由
    from app.api.routes import api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')

    # 健康检查路由
    @app.route('/health')
    def health_check():
        return {'status': 'ok', 'message': 'Service is running'}

    # 简单的健康检查路由（只返回 ok）
    @app.route('/health/simple')
    def simple_health():
        return 'ok'

    return app