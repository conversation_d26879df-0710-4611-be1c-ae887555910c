#!/usr/bin/env python3
"""
测试文件管理接口
测试 CSV 下载和图片预览功能
"""
import requests
import json
import os


def test_file_management():
    """测试文件管理功能"""
    base_url = "http://localhost:8000/api/v1"
    
    # 请替换为你的实际 API Key
    api_key = "your-api-key-here"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📁 测试文件管理接口")
    print("="*60)
    
    # 1. 测试获取文件列表
    print("\n1️⃣ 测试获取文件列表...")
    
    try:
        response = requests.get(
            f"{base_url}/files/list",
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            
            print("✅ 文件列表获取成功")
            print(f"数据目录: {data.get('data_directory', 'N/A')}")
            print(f"总文件数: {data.get('total_files', 0)}")
            print(f"CSV 文件数: {data.get('csv_count', 0)}")
            print(f"图片文件数: {data.get('image_count', 0)}")
            
            # 显示 CSV 文件
            csv_files = data.get('csv_files', [])
            if csv_files:
                print(f"\n📊 CSV 文件列表:")
                for i, file_info in enumerate(csv_files[:5], 1):  # 只显示前5个
                    print(f"  {i}. {file_info['filename']}")
                    print(f"     大小: {file_info['size_human']}")
                    print(f"     修改时间: {file_info['modified_time']}")
                    print(f"     下载链接: {base_url}{file_info['download_url']}")
                
                if len(csv_files) > 5:
                    print(f"     ... 还有 {len(csv_files) - 5} 个文件")
            else:
                print(f"\n📊 没有找到 CSV 文件")
            
            # 显示图片文件
            image_files = data.get('image_files', [])
            if image_files:
                print(f"\n🖼️  图片文件列表:")
                for i, file_info in enumerate(image_files[:5], 1):  # 只显示前5个
                    print(f"  {i}. {file_info['filename']}")
                    print(f"     大小: {file_info['size_human']}")
                    print(f"     修改时间: {file_info['modified_time']}")
                    print(f"     预览链接: {base_url}{file_info['preview_url']}")
                    print(f"     下载链接: {base_url}{file_info['download_url']}")
                
                if len(image_files) > 5:
                    print(f"     ... 还有 {len(image_files) - 5} 个文件")
            else:
                print(f"\n🖼️  没有找到图片文件")
            
            # 保存文件信息用于后续测试
            global test_csv_file, test_image_file
            test_csv_file = csv_files[0]['filename'] if csv_files else None
            test_image_file = image_files[0]['filename'] if image_files else None
            
        else:
            error_info = response.json()
            print("❌ 获取文件列表失败")
            print(f"错误: {error_info.get('message', 'Unknown error')}")
            return
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return
    
    # 2. 测试文件下载
    if test_csv_file:
        print(f"\n2️⃣ 测试 CSV 文件下载...")
        print(f"下载文件: {test_csv_file}")
        
        try:
            response = requests.get(
                f"{base_url}/files/download/{test_csv_file}",
                headers=headers,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 文件下载成功")
                print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
                print(f"Content-Length: {response.headers.get('Content-Length', 'N/A')} bytes")
                
                # 保存文件到本地（可选）
                download_path = f"downloaded_{test_csv_file}"
                with open(download_path, 'wb') as f:
                    f.write(response.content)
                print(f"文件已保存到: {download_path}")
                
            else:
                error_info = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}
                print("❌ 文件下载失败")
                print(f"错误: {error_info.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 下载异常: {str(e)}")
    else:
        print(f"\n2️⃣ 跳过 CSV 文件下载测试（没有 CSV 文件）")
    
    # 3. 测试图片预览
    if test_image_file:
        print(f"\n3️⃣ 测试图片预览...")
        print(f"预览文件: {test_image_file}")
        
        try:
            response = requests.get(
                f"{base_url}/files/preview/{test_image_file}",
                headers=headers,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 图片预览成功")
                print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
                print(f"Content-Length: {response.headers.get('Content-Length', 'N/A')} bytes")
                
                # 保存图片到本地（可选）
                preview_path = f"preview_{test_image_file}"
                with open(preview_path, 'wb') as f:
                    f.write(response.content)
                print(f"图片已保存到: {preview_path}")
                
            else:
                error_info = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}
                print("❌ 图片预览失败")
                print(f"错误: {error_info.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 预览异常: {str(e)}")
    else:
        print(f"\n3️⃣ 跳过图片预览测试（没有图片文件）")
    
    # 4. 测试错误情况
    print(f"\n4️⃣ 测试错误情况...")
    
    # 测试下载不存在的文件
    print(f"\n  测试下载不存在的文件...")
    try:
        response = requests.get(
            f"{base_url}/files/download/nonexistent_file.csv",
            headers=headers,
            timeout=10
        )
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 404:
            print("  ✅ 正确返回 404 错误")
        else:
            print("  ❌ 应该返回 404 错误")
            
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")
    
    # 测试路径遍历攻击
    print(f"\n  测试路径遍历攻击防护...")
    try:
        response = requests.get(
            f"{base_url}/files/download/../config.py",
            headers=headers,
            timeout=10
        )
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 400:
            print("  ✅ 正确阻止了路径遍历攻击")
        else:
            print("  ❌ 应该阻止路径遍历攻击")
            
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")


def create_sample_files():
    """创建示例文件用于测试"""
    print(f"\n📝 创建示例文件...")
    
    # 创建 data 目录
    data_dir = "data"
    os.makedirs(data_dir, exist_ok=True)
    
    # 创建示例 CSV 文件
    csv_content = """id,name,email,age
1,张三,<EMAIL>,25
2,李四,<EMAIL>,30
3,王五,<EMAIL>,28
"""
    
    csv_file = os.path.join(data_dir, "sample_users.csv")
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    print(f"创建 CSV 文件: {csv_file}")
    
    # 创建示例图片文件（简单的 SVG）
    svg_content = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="100" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="100" fill="#4CAF50"/>
  <text x="100" y="55" font-family="Arial" font-size="16" fill="white" text-anchor="middle">
    Sample Image
  </text>
</svg>
"""
    
    svg_file = os.path.join(data_dir, "sample_chart.svg")
    with open(svg_file, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    print(f"创建 SVG 文件: {svg_file}")
    
    print(f"示例文件创建完成！")


def show_usage_examples():
    """显示使用示例"""
    print(f"\n" + "="*60)
    print("📚 使用示例")
    print("="*60)
    
    print("\n1. 获取文件列表:")
    print("""
curl -X GET http://localhost:8000/api/v1/files/list \\
  -H "Authorization: Bearer your-api-key"
""")
    
    print("\n2. 下载 CSV 文件:")
    print("""
curl -X GET http://localhost:8000/api/v1/files/download/sample_users.csv \\
  -H "Authorization: Bearer your-api-key" \\
  -o downloaded_file.csv
""")
    
    print("\n3. 预览图片:")
    print("""
curl -X GET http://localhost:8000/api/v1/files/preview/sample_chart.svg \\
  -H "Authorization: Bearer your-api-key" \\
  -o preview_image.svg
""")
    
    print("\n4. 在浏览器中查看:")
    print("- 文件列表: http://localhost:8000/api/v1/files/list")
    print("- 图片预览: http://localhost:8000/api/v1/files/preview/sample_chart.svg")
    print("- 文件下载: http://localhost:8000/api/v1/files/download/sample_users.csv")
    print("  (需要在请求头中添加 Authorization: Bearer your-api-key)")


if __name__ == '__main__':
    try:
        # 询问是否创建示例文件
        create_samples = input("是否创建示例文件用于测试？(y/n): ").strip().lower()
        if create_samples in ['y', 'yes']:
            create_sample_files()
        
        test_file_management()
        show_usage_examples()
        
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行在 http://localhost:8000")
        print("2. 已配置有效的 API Key")
        print("3. data 目录中有文件可供测试")
        print("4. 网络连接正常")
