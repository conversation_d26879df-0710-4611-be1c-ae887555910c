# 训练数据管理 API 指南

## 概述

新增了两个 API 接口用于管理训练数据：
- 获取训练数据列表
- 删除指定的训练数据

这些接口基于 Vanna.ai 的 `get_training_data()` 和 `remove_training_data()` 方法实现。

## 🔗 API 接口

### 1. 获取训练数据列表

#### 接口信息
- **路径**: `GET /api/v1/training-data`
- **认证**: 需要 API Key
- **功能**: 获取当前系统中的所有训练数据

#### 请求参数 (查询参数)
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `VANNA_MYSQL_host` | string | 否 | MySQL 主机地址 |
| `VANNA_MYSQL_port` | integer | 否 | MySQL 端口 |
| `VANNA_MYSQL_user` | string | 否 | MySQL 用户名 |
| `VANNA_MYSQL_password` | string | 否 | MySQL 密码 |
| `VANNA_MYSQL_database` | string | 否 | MySQL 数据库名 |

#### 请求示例
```bash
# 基本请求
curl -X GET "http://localhost:8001/api/v1/training-data" \
  -H "Authorization: Bearer your-api-key"

# 带 MySQL 参数
curl -X GET "http://localhost:8001/api/v1/training-data?VANNA_MYSQL_host=localhost&VANNA_MYSQL_database=vanna_db" \
  -H "Authorization: Bearer your-api-key"
```

#### 响应格式
```json
{
  "success": true,
  "message": "训练数据获取成功",
  "data": {
    "training_data": [
      {
        "id": "training-data-id-1",
        "training_data_type": "ddl",
        "question": "创建用户表",
        "content": "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100))",
        "created_at": "2023-12-01T10:00:00Z"
      },
      {
        "id": "training-data-id-2", 
        "training_data_type": "sql",
        "question": "查询所有用户",
        "content": "SELECT * FROM users",
        "created_at": "2023-12-01T10:05:00Z"
      }
    ],
    "count": 2,
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "vanna_user",
      "database": "vanna_db"
    }
  }
}
```

### 2. 删除训练数据

#### 接口信息
- **路径**: `DELETE /api/v1/training-data/{training_id}`
- **认证**: 需要 API Key
- **功能**: 删除指定 ID 的训练数据

### 3. 删除所有训练数据 ⚠️

#### 接口信息
- **路径**: `DELETE /api/v1/training-data/clear-all`
- **认证**: 需要 API Key
- **功能**: 删除所有训练数据（危险操作）
- **⚠️ 警告**: 此操作不可恢复，请谨慎使用！

#### 路径参数
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `training_id` | string | 是 | 要删除的训练数据 ID |

#### 查询参数 (可选)
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `VANNA_MYSQL_host` | string | 否 | MySQL 主机地址 |
| `VANNA_MYSQL_port` | integer | 否 | MySQL 端口 |
| `VANNA_MYSQL_user` | string | 否 | MySQL 用户名 |
| `VANNA_MYSQL_password` | string | 否 | MySQL 密码 |
| `VANNA_MYSQL_database` | string | 否 | MySQL 数据库名 |

#### 请求示例
```bash
# 基本删除
curl -X DELETE "http://localhost:8001/api/v1/training-data/training-data-id-1" \
  -H "Authorization: Bearer your-api-key"

# 带 MySQL 参数删除
curl -X DELETE "http://localhost:8001/api/v1/training-data/training-data-id-1?VANNA_MYSQL_host=localhost" \
  -H "Authorization: Bearer your-api-key"
```

#### 响应格式
```json
{
  "success": true,
  "message": "训练数据 training-data-id-1 删除成功",
  "data": {
    "training_id": "training-data-id-1",
    "result": true,
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "vanna_user",
      "database": "vanna_db"
    }
  }
}
```

#### 查询参数 (必需)
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `confirm` | string | 是 | 必须为 'yes' 才能执行删除 |

#### 查询参数 (可选)
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `VANNA_MYSQL_host` | string | 否 | MySQL 主机地址 |
| `VANNA_MYSQL_port` | integer | 否 | MySQL 端口 |
| `VANNA_MYSQL_user` | string | 否 | MySQL 用户名 |
| `VANNA_MYSQL_password` | string | 否 | MySQL 密码 |
| `VANNA_MYSQL_database` | string | 否 | MySQL 数据库名 |

#### 请求示例
```bash
# ⚠️ 危险操作：删除所有训练数据
curl -X DELETE "http://localhost:8001/api/v1/training-data/clear-all?confirm=yes" \
  -H "Authorization: Bearer your-api-key"

# 带 MySQL 参数
curl -X DELETE "http://localhost:8001/api/v1/training-data/clear-all?confirm=yes&VANNA_MYSQL_host=localhost" \
  -H "Authorization: Bearer your-api-key"
```

#### 响应格式
```json
{
  "success": true,
  "message": "删除操作完成：删除完成，成功删除 5 条，失败 0 条",
  "data": {
    "operation": "clear_all_training_data",
    "deleted_count": 5,
    "failed_count": 0,
    "total_count": 5,
    "deleted_ids": [
      "training-id-1",
      "training-id-2",
      "training-id-3",
      "training-id-4",
      "training-id-5"
    ],
    "failed_ids": [],
    "message": "删除完成，成功删除 5 条，失败 0 条",
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "vanna_user",
      "database": "vanna_db"
    }
  }
}
```

#### 安全机制
1. **确认参数**: 必须提供 `confirm=yes` 参数
2. **操作日志**: 所有删除操作都会记录到日志
3. **详细统计**: 返回删除成功和失败的详细信息
4. **错误处理**: 单个删除失败不会影响其他数据的删除

## 🐍 Python 使用示例

### 获取训练数据列表
```python
import requests

# 配置
base_url = "http://localhost:8001"
api_key = "your-api-key"
headers = {"Authorization": f"Bearer {api_key}"}

# 获取训练数据列表
response = requests.get(f"{base_url}/api/v1/training-data", headers=headers)

if response.status_code == 200:
    data = response.json()
    training_data = data['data']['training_data']
    print(f"找到 {len(training_data)} 条训练数据")
    
    for item in training_data:
        print(f"ID: {item['id']}, 类型: {item['training_data_type']}")
else:
    print(f"请求失败: {response.status_code}")
```

### 删除训练数据
```python
import requests

# 配置
base_url = "http://localhost:8001"
api_key = "your-api-key"
headers = {"Authorization": f"Bearer {api_key}"}

# 删除指定训练数据
training_id = "training-data-id-1"
response = requests.delete(
    f"{base_url}/api/v1/training-data/{training_id}", 
    headers=headers
)

if response.status_code == 200:
    data = response.json()
    print(f"删除成功: {data['message']}")
else:
    print(f"删除失败: {response.status_code}")
```

### 删除所有训练数据
```python
import requests

# 配置
base_url = "http://localhost:8001"
api_key = "your-api-key"
headers = {"Authorization": f"Bearer {api_key}"}

# ⚠️ 危险操作：删除所有训练数据
def clear_all_training_data():
    # 首先获取当前数据数量
    response = requests.get(f"{base_url}/api/v1/training-data", headers=headers)
    if response.status_code == 200:
        count = response.json()['data']['count']
        print(f"当前有 {count} 条训练数据")

        if count == 0:
            print("没有数据需要删除")
            return

        # 确认删除
        confirm = input(f"确认删除所有 {count} 条训练数据? (yes/no): ")
        if confirm.lower() != 'yes':
            print("操作已取消")
            return

        # 执行删除
        response = requests.delete(
            f"{base_url}/api/v1/training-data/clear-all?confirm=yes",
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()['data']
            print(f"删除完成:")
            print(f"  总数量: {result['total_count']}")
            print(f"  成功删除: {result['deleted_count']}")
            print(f"  删除失败: {result['failed_count']}")
        else:
            print(f"删除失败: {response.status_code}")
    else:
        print(f"获取训练数据失败: {response.status_code}")

# 使用示例
clear_all_training_data()
```

### 批量管理训练数据
```python
import requests

def get_all_training_data(base_url, api_key):
    """获取所有训练数据"""
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.get(f"{base_url}/api/v1/training-data", headers=headers)
    
    if response.status_code == 200:
        return response.json()['data']['training_data']
    else:
        raise Exception(f"获取训练数据失败: {response.status_code}")

def delete_training_data(base_url, api_key, training_id):
    """删除指定训练数据"""
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.delete(
        f"{base_url}/api/v1/training-data/{training_id}", 
        headers=headers
    )
    
    return response.status_code == 200

def cleanup_old_training_data(base_url, api_key, data_type=None):
    """清理旧的训练数据"""
    training_data = get_all_training_data(base_url, api_key)
    
    deleted_count = 0
    for item in training_data:
        if data_type is None or item.get('training_data_type') == data_type:
            if delete_training_data(base_url, api_key, item['id']):
                deleted_count += 1
                print(f"已删除: {item['id']}")
    
    print(f"总共删除了 {deleted_count} 条训练数据")

# 使用示例
base_url = "http://localhost:8001"
api_key = "your-api-key"

# 清理所有 DDL 类型的训练数据
cleanup_old_training_data(base_url, api_key, data_type="ddl")
```

## 🔍 错误处理

### 常见错误码
| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| `INVALID_MYSQL_CONFIG` | 400 | MySQL 配置无效 |
| `TRAINING_DATA_PARAM_ERROR` | 400 | 请求参数错误 |
| `MISSING_TRAINING_ID` | 400 | 缺少训练数据 ID |
| `TRAINING_DATA_RUNTIME_ERROR` | 500 | 运行时错误 |
| `TRAINING_DATA_ERROR` | 500 | 获取训练数据失败 |
| `REMOVE_TRAINING_DATA_ERROR` | 500 | 删除训练数据失败 |
| `CONFIRMATION_REQUIRED` | 400 | 删除所有数据需要确认 |
| `CLEAR_ALL_TRAINING_DATA_PARAM_ERROR` | 400 | 删除所有数据参数错误 |
| `CLEAR_ALL_TRAINING_DATA_RUNTIME_ERROR` | 500 | 删除所有数据运行时错误 |
| `CLEAR_ALL_TRAINING_DATA_ERROR` | 500 | 删除所有数据失败 |

### 错误响应示例
```json
{
  "success": false,
  "message": "参数错误: 删除训练数据需要提供 ID",
  "error_code": "TRAINING_DATA_PARAM_ERROR",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 🧪 测试

### 运行测试脚本
```bash
python test_training_data_api.py
```

### 手动测试步骤
1. **获取训练数据列表**
   ```bash
   curl -X GET "http://localhost:8001/api/v1/training-data" \
     -H "Authorization: Bearer your-api-key"
   ```

2. **记录训练数据 ID**
   从响应中找到要删除的训练数据 ID

3. **删除训练数据**
   ```bash
   curl -X DELETE "http://localhost:8001/api/v1/training-data/TRAINING_ID_HERE" \
     -H "Authorization: Bearer your-api-key"
   ```

4. **验证删除结果**
   再次获取训练数据列表，确认数据已被删除

5. **删除所有训练数据** ⚠️
   ```bash
   # 危险操作：删除所有训练数据
   curl -X DELETE "http://localhost:8001/api/v1/training-data/clear-all?confirm=yes" \
     -H "Authorization: Bearer your-api-key"
   ```

6. **测试删除所有数据**
   ```bash
   # 运行专门的测试脚本
   python test_clear_all_training_data.py
   ```

## ⚠️ 注意事项

### 1. 数据安全
- 删除操作是不可逆的，请谨慎操作
- 建议在删除前备份重要的训练数据
- 考虑实现软删除机制（标记为删除而不是物理删除）
- **删除所有数据需要 `confirm=yes` 参数**
- **删除所有数据的操作会被记录到警告日志中**

### 2. 性能考虑
- 获取大量训练数据可能需要较长时间
- 考虑实现分页功能
- 对于频繁的删除操作，考虑批量删除接口

### 3. 权限控制
- 确保只有授权用户可以删除训练数据
- 考虑实现不同级别的权限控制
- 记录删除操作的审计日志

### 4. 错误处理
- 删除不存在的训练数据应返回适当的错误信息
- 网络异常时应有重试机制
- 提供详细的错误信息便于调试

## 🔄 工作流程示例

### 训练数据清理工作流
```python
# 1. 获取所有训练数据
training_data = get_all_training_data(base_url, api_key)

# 2. 筛选需要删除的数据
to_delete = []
for item in training_data:
    # 例如：删除超过30天的旧数据
    if is_old_data(item):
        to_delete.append(item['id'])

# 3. 确认删除
print(f"将要删除 {len(to_delete)} 条训练数据")
confirm = input("确认删除? (y/N): ")

if confirm.lower() == 'y':
    # 4. 执行删除
    for training_id in to_delete:
        delete_training_data(base_url, api_key, training_id)
        print(f"已删除: {training_id}")
```

## 📚 相关文档

- [API 认证指南](API_KEY_GUIDE.md)
- [训练 API 指南](TRAIN_API_LOGGING_GUIDE.md)
- [MySQL 连接配置](MYSQL_PARAMS_GUIDE.md)
- [错误处理指南](TROUBLESHOOTING_ASK_API.md)

## 总结

新增的训练数据管理接口提供了：

- ✅ **完整的训练数据查看** - 获取所有训练数据的详细信息
- ✅ **精确的数据删除** - 根据 ID 删除指定的训练数据
- ✅ **批量数据清理** - 一次性删除所有训练数据（带安全确认）
- ✅ **灵活的 MySQL 配置** - 支持动态 MySQL 连接参数
- ✅ **统一的错误处理** - 一致的错误响应格式
- ✅ **安全的 API 认证** - 需要有效的 API Key
- ✅ **详细的操作日志** - 记录所有操作用于审计
- ✅ **安全确认机制** - 危险操作需要明确确认
- ✅ **详细的删除统计** - 提供成功/失败的详细信息

这些接口使得训练数据的管理更加便捷和安全！
