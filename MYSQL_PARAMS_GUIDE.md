# MySQL 连接参数使用指南

## 概述

`train` 和 `ask` 接口现在支持动态传入 MySQL 连接参数，允许您在运行时指定不同的数据库连接，而不需要修改服务器配置。

## 支持的参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `mysql_host` | string | 是* | 环境变量配置 | MySQL 服务器主机地址 |
| `mysql_port` | integer | 否 | 3306 | MySQL 服务器端口号 |
| `mysql_user` | string | 是* | 环境变量配置 | MySQL 用户名 |
| `mysql_password` | string | 否 | 环境变量配置 | MySQL 密码 |
| `mysql_database` | string | 是* | 环境变量配置 | MySQL 数据库名 |

*必需参数：如果环境变量中没有配置，则必须在请求中提供

## 参数优先级

1. **请求参数** - 在 API 请求中直接传入的参数具有最高优先级
2. **环境变量** - 未在请求中指定的参数将使用 `.env` 文件中的配置
3. **默认值** - 某些参数（如 `mysql_port`）有内置默认值

## 使用示例

### 0. 连接测试接口 (test-connection)

在使用训练和查询接口之前，建议先测试 MySQL 连接是否正常。

#### 测试默认配置
```bash
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 测试自定义配置
```bash
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "mysql_host": "*************",
    "mysql_port": 3306,
    "mysql_user": "test_user",
    "mysql_password": "test_password",
    "mysql_database": "test_db"
  }'
```

#### 成功响应示例
```json
{
  "success": true,
  "message": "MySQL 连接测试成功",
  "data": {
    "connection_status": "success",
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "root",
      "database": "test"
    },
    "server_info": {
      "version": "8.0.33",
      "host": "localhost",
      "port": 3306
    },
    "database_exists": true,
    "response_time_ms": 15.23
  }
}
```

### 1. 训练接口 (train)

#### 使用默认配置
```bash
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(100));",
    "documentation": "用户表包含用户的基本信息"
  }'
```

#### 使用自定义 MySQL 配置
```bash
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE products (id INT PRIMARY KEY, name VARCHAR(200), price DECIMAL(10,2));",
    "documentation": "产品表",
    "mysql_host": "*************",
    "mysql_port": 3306,
    "mysql_user": "analytics_user",
    "mysql_password": "secure_password",
    "mysql_database": "analytics_db"
  }'
```

#### 部分自定义配置
```bash
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE orders (id INT PRIMARY KEY, user_id INT, total DECIMAL(10,2));",
    "documentation": "订单表",
    "mysql_host": "production-db.company.com",
    "mysql_database": "orders_db"
  }'
```

### 2. 查询接口 (ask)

#### 使用默认配置
```bash
curl -X POST http://localhost:8000/api/v1/ask \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户的姓名和邮箱"
  }'
```

#### 使用自定义 MySQL 配置
```bash
curl -X POST http://localhost:8000/api/v1/ask \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询销售额最高的前10个产品",
    "mysql_host": "analytics-db.company.com",
    "mysql_user": "readonly_user",
    "mysql_password": "readonly_pass",
    "mysql_database": "sales_analytics"
  }'
```

## Python 示例

### 使用 requests 库

```python
import requests

# 配置
api_key = "your-api-key-here"
base_url = "http://localhost:8000/api/v1"
headers = {"Authorization": f"Bearer {api_key}"}

# 训练示例 - 使用自定义数据库
train_data = {
    "ddl": "CREATE TABLE customers (id INT, name VARCHAR(100), city VARCHAR(50));",
    "documentation": "客户表包含客户基本信息和所在城市",
    "mysql_host": "customer-db.company.com",
    "mysql_database": "customer_data",
    "mysql_user": "app_user"
}

response = requests.post(f"{base_url}/train", headers=headers, json=train_data)
print("训练响应:", response.json())

# 查询示例 - 连接到不同的数据库
ask_data = {
    "question": "查询北京的所有客户",
    "mysql_host": "reporting-db.company.com",
    "mysql_database": "reports",
    "mysql_user": "report_user"
}

response = requests.post(f"{base_url}/ask", headers=headers, json=ask_data)
print("查询响应:", response.json())
```

## 响应格式

### 成功响应

接口会返回使用的 MySQL 配置信息（不包含密码）：

```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "result": {
      "sql": "SELECT name, email FROM users;",
      "data": [...],
      "chart": null
    },
    "mysql_config": {
      "host": "*************",
      "port": 3306,
      "user": "analytics_user",
      "database": "analytics_db"
    },
    "question": "查询所有用户的姓名和邮箱"
  }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "缺少必需的 MySQL 配置参数: host, user, database",
  "error_code": "INVALID_MYSQL_CONFIG",
  "data": null
}
```

## 使用场景

### 1. 多环境部署
```python
# 开发环境
dev_config = {
    "mysql_host": "localhost",
    "mysql_database": "dev_db"
}

# 生产环境
prod_config = {
    "mysql_host": "prod-db.company.com",
    "mysql_database": "production_db",
    "mysql_user": "prod_user"
}
```

### 2. 多租户系统
```python
# 为不同客户使用不同数据库
tenant_configs = {
    "tenant_a": {
        "mysql_host": "tenant-a-db.company.com",
        "mysql_database": "tenant_a_data"
    },
    "tenant_b": {
        "mysql_host": "tenant-b-db.company.com", 
        "mysql_database": "tenant_b_data"
    }
}
```

### 3. 读写分离
```python
# 训练使用主库
train_config = {
    "mysql_host": "master-db.company.com",
    "mysql_user": "write_user"
}

# 查询使用从库
query_config = {
    "mysql_host": "slave-db.company.com",
    "mysql_user": "readonly_user"
}
```

## 安全注意事项

1. **密码保护** - 响应中不会返回密码信息
2. **连接验证** - 服务器会验证 MySQL 连接参数的有效性
3. **权限控制** - 确保使用的 MySQL 用户具有适当的权限
4. **网络安全** - 在生产环境中使用 HTTPS 传输敏感信息

## 故障排除

### 常见错误

1. **连接失败**
   ```
   错误: 无法连接到 MySQL 服务器
   解决: 检查主机地址、端口和网络连接
   ```

2. **认证失败**
   ```
   错误: MySQL 用户认证失败
   解决: 检查用户名和密码是否正确
   ```

3. **数据库不存在**
   ```
   错误: 指定的数据库不存在
   解决: 确认数据库名称正确且存在
   ```

### 调试技巧

1. **使用部分参数** - 先只传入 `mysql_host` 测试连接
2. **检查日志** - 查看服务器日志获取详细错误信息
3. **验证配置** - 使用 MySQL 客户端工具验证连接参数

## 测试

### 测试 MySQL 连接

运行连接测试脚本：

```bash
python test_mysql_connection.py
```

这个脚本会测试 MySQL 连接接口的各种情况，包括：
- 默认配置连接测试
- 自定义配置连接测试
- 错误情况测试（无效主机、错误端口等）

### 测试 MySQL 参数功能

运行参数功能测试脚本：

```bash
python test_mysql_params.py
```

这个脚本会测试 `train` 和 `ask` 接口的各种 MySQL 参数组合和错误情况。

### 推荐的测试流程

1. **首先测试连接** - 使用 `test-connection` 接口验证 MySQL 配置
2. **然后测试功能** - 使用 `train` 和 `ask` 接口进行实际操作
3. **监控日志** - 查看服务器日志确认操作正常
