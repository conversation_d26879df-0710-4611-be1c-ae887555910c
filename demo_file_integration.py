#!/usr/bin/env python3
"""
演示文件管理接口的集成使用
展示如何在实际应用中使用文件列表、下载和预览功能
"""
import requests
import json
import os


class VannaFileManager:
    """Vanna 文件管理客户端"""
    
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_file_list(self):
        """获取文件列表"""
        try:
            response = requests.get(
                f"{self.base_url}/files/list",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()['data']
            else:
                error_info = response.json()
                raise Exception(f"获取文件列表失败: {error_info.get('message', 'Unknown error')}")
                
        except Exception as e:
            raise Exception(f"请求异常: {str(e)}")
    
    def download_file(self, filename, save_path=None):
        """下载文件"""
        try:
            response = requests.get(
                f"{self.base_url}/files/download/{filename}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                if save_path is None:
                    save_path = filename
                
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                
                return {
                    'success': True,
                    'file_path': save_path,
                    'size': len(response.content),
                    'content_type': response.headers.get('Content-Type')
                }
            else:
                error_info = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}
                raise Exception(f"下载失败: {error_info.get('message', 'Unknown error')}")
                
        except Exception as e:
            raise Exception(f"下载异常: {str(e)}")
    
    def get_preview_url(self, filename):
        """获取预览 URL"""
        return f"{self.base_url}/files/preview/{filename}"
    
    def get_download_url(self, filename):
        """获取下载 URL"""
        return f"{self.base_url}/files/download/{filename}"


def demo_file_integration():
    """演示文件管理集成"""
    print("🔗 Vanna 文件管理集成演示")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8000/api/v1"
    api_key = input("请输入你的 API Key: ").strip()
    
    if not api_key:
        print("❌ 未提供 API Key，使用示例密钥")
        api_key = "your-api-key-here"
    
    # 创建文件管理客户端
    file_manager = VannaFileManager(base_url, api_key)
    
    try:
        # 1. 获取文件列表
        print(f"\n1️⃣ 获取文件列表...")
        file_data = file_manager.get_file_list()
        
        print(f"✅ 文件列表获取成功")
        print(f"数据目录: {file_data['data_directory']}")
        print(f"总文件数: {file_data['total_files']}")
        
        # 2. 展示 CSV 文件管理
        csv_files = file_data['csv_files']
        if csv_files:
            print(f"\n2️⃣ CSV 文件管理 ({len(csv_files)} 个文件)")
            
            for i, csv_file in enumerate(csv_files[:3], 1):  # 只处理前3个
                print(f"\n  📊 CSV 文件 {i}: {csv_file['filename']}")
                print(f"     大小: {csv_file['size_human']}")
                print(f"     修改时间: {csv_file['modified_time']}")
                
                # 提供下载选项
                download_choice = input(f"     是否下载此文件？(y/n): ").strip().lower()
                if download_choice in ['y', 'yes']:
                    try:
                        result = file_manager.download_file(csv_file['filename'])
                        print(f"     ✅ 下载成功: {result['file_path']}")
                        print(f"     文件大小: {result['size']} bytes")
                        
                        # 显示文件内容预览（如果是 CSV）
                        if csv_file['filename'].endswith('.csv'):
                            try:
                                with open(result['file_path'], 'r', encoding='utf-8') as f:
                                    lines = f.readlines()[:5]  # 只显示前5行
                                print(f"     📄 文件内容预览:")
                                for line in lines:
                                    print(f"       {line.strip()}")
                                if len(lines) == 5:
                                    print(f"       ...")
                            except Exception as e:
                                print(f"     ⚠️  无法预览文件内容: {str(e)}")
                        
                    except Exception as e:
                        print(f"     ❌ 下载失败: {str(e)}")
        else:
            print(f"\n2️⃣ 没有找到 CSV 文件")
        
        # 3. 展示图片文件管理
        image_files = file_data['image_files']
        if image_files:
            print(f"\n3️⃣ 图片文件管理 ({len(image_files)} 个文件)")
            
            for i, image_file in enumerate(image_files[:3], 1):  # 只处理前3个
                print(f"\n  🖼️  图片文件 {i}: {image_file['filename']}")
                print(f"     大小: {image_file['size_human']}")
                print(f"     修改时间: {image_file['modified_time']}")
                print(f"     预览 URL: {file_manager.get_preview_url(image_file['filename'])}")
                print(f"     下载 URL: {file_manager.get_download_url(image_file['filename'])}")
                
                # 提供预览选项
                preview_choice = input(f"     是否下载此图片？(y/n): ").strip().lower()
                if preview_choice in ['y', 'yes']:
                    try:
                        result = file_manager.download_file(image_file['filename'])
                        print(f"     ✅ 下载成功: {result['file_path']}")
                        print(f"     文件大小: {result['size']} bytes")
                        print(f"     内容类型: {result['content_type']}")
                        
                    except Exception as e:
                        print(f"     ❌ 下载失败: {str(e)}")
        else:
            print(f"\n3️⃣ 没有找到图片文件")
        
        # 4. 生成前端集成代码示例
        print(f"\n4️⃣ 前端集成示例")
        generate_frontend_examples(file_data, base_url, api_key)
        
    except Exception as e:
        print(f"❌ 演示错误: {str(e)}")


def generate_frontend_examples(file_data, base_url, api_key):
    """生成前端集成代码示例"""
    print(f"\n📝 前端集成代码示例:")
    
    # JavaScript 示例
    print(f"\n1. JavaScript 获取文件列表:")
    print(f"""
const apiKey = '{api_key}';
const baseUrl = '{base_url}';

// 获取文件列表
async function getFileList() {{
    const response = await fetch(`${{baseUrl}}/files/list`, {{
        headers: {{
            'Authorization': `Bearer ${{apiKey}}`
        }}
    }});
    
    const result = await response.json();
    if (result.success) {{
        return result.data;
    }} else {{
        throw new Error(result.message);
    }}
}}

// 使用示例
getFileList().then(data => {{
    console.log('CSV 文件:', data.csv_files);
    console.log('图片文件:', data.image_files);
}});
""")
    
    # HTML 示例
    print(f"\n2. HTML 显示文件列表:")
    print(f"""
<!-- CSV 文件列表 -->
<div id="csv-files">
    <h3>CSV 文件</h3>
    <ul id="csv-list"></ul>
</div>

<!-- 图片文件列表 -->
<div id="image-files">
    <h3>图片文件</h3>
    <div id="image-gallery"></div>
</div>

<script>
// 渲染 CSV 文件列表
function renderCSVFiles(csvFiles) {{
    const csvList = document.getElementById('csv-list');
    csvList.innerHTML = '';
    
    csvFiles.forEach(file => {{
        const li = document.createElement('li');
        li.innerHTML = `
            <span>${{file.filename}} (${{file.size_human}})</span>
            <a href="${{baseUrl}}${{file.download_url}}" 
               download="${{file.filename}}"
               onclick="addAuthHeader(event)">下载</a>
        `;
        csvList.appendChild(li);
    }});
}}

// 渲染图片文件列表
function renderImageFiles(imageFiles) {{
    const gallery = document.getElementById('image-gallery');
    gallery.innerHTML = '';
    
    imageFiles.forEach(file => {{
        const div = document.createElement('div');
        div.innerHTML = `
            <img src="${{baseUrl}}${{file.preview_url}}" 
                 alt="${{file.filename}}"
                 style="max-width: 200px; max-height: 200px;"
                 onload="addAuthToImage(this)">
            <p>${{file.filename}} (${{file.size_human}})</p>
            <a href="${{baseUrl}}${{file.download_url}}" 
               download="${{file.filename}}"
               onclick="addAuthHeader(event)">下载</a>
        `;
        gallery.appendChild(div);
    }});
}}

// 为下载链接添加认证头
function addAuthHeader(event) {{
    // 注意：浏览器的下载链接无法直接添加自定义头
    // 需要使用 fetch API 下载文件
    event.preventDefault();
    const url = event.target.href;
    downloadFile(url, event.target.download);
}}

// 使用 fetch 下载文件
async function downloadFile(url, filename) {{
    const response = await fetch(url, {{
        headers: {{
            'Authorization': `Bearer ${{apiKey}}`
        }}
    }});
    
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
}}
</script>
""")
    
    # React 示例
    if file_data['csv_files'] or file_data['image_files']:
        print(f"\n3. React 组件示例:")
        print(f"""
import React, {{ useState, useEffect }} from 'react';

const FileManager = () => {{
    const [files, setFiles] = useState({{ csv_files: [], image_files: [] }});
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {{
        fetchFiles();
    }}, []);
    
    const fetchFiles = async () => {{
        try {{
            const response = await fetch('{base_url}/files/list', {{
                headers: {{
                    'Authorization': 'Bearer {api_key}'
                }}
            }});
            const result = await response.json();
            if (result.success) {{
                setFiles(result.data);
            }}
        }} catch (error) {{
            console.error('获取文件列表失败:', error);
        }} finally {{
            setLoading(false);
        }}
    }};
    
    const downloadFile = async (filename) => {{
        try {{
            const response = await fetch(`{base_url}/files/download/${{filename}}`, {{
                headers: {{
                    'Authorization': 'Bearer {api_key}'
                }}
            }});
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }} catch (error) {{
            console.error('下载失败:', error);
        }}
    }};
    
    if (loading) return <div>加载中...</div>;
    
    return (
        <div>
            <h2>文件管理</h2>
            
            <h3>CSV 文件 ({{files.csv_files.length}})</h3>
            <ul>
                {{files.csv_files.map(file => (
                    <li key={{file.filename}}>
                        <span>{{file.filename}} ({{file.size_human}})</span>
                        <button onClick={{() => downloadFile(file.filename)}}>
                            下载
                        </button>
                    </li>
                ))}}
            </ul>
            
            <h3>图片文件 ({{files.image_files.length}})</h3>
            <div style={{{{display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: '10px'}}}}>
                {{files.image_files.map(file => (
                    <div key={{file.filename}}>
                        <img 
                            src={{`{base_url}${{file.preview_url}}`}}
                            alt={{file.filename}}
                            style={{{{maxWidth: '100%', height: '150px', objectFit: 'cover'}}}}
                        />
                        <p>{{file.filename}}</p>
                        <button onClick={{() => downloadFile(file.filename)}}>
                            下载
                        </button>
                    </div>
                ))}}
            </div>
        </div>
    );
}};

export default FileManager;
""")


if __name__ == '__main__':
    try:
        demo_file_integration()
    except KeyboardInterrupt:
        print("\n\n演示已取消")
    except Exception as e:
        print(f"\n❌ 演示错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行")
        print("2. 已配置有效的 API Key")
        print("3. data 目录中有文件")
        print("4. 网络连接正常")
