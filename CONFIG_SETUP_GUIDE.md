# 配置文件设置指南

## 概述

配置文件已移动到 `config/` 目录中，以便更好地组织项目结构。

## 📁 新的配置文件结构

```
project/
├── config/
│   ├── .env.example    # 环境变量配置模板
│   └── .env           # 实际环境变量配置 (需要创建)
├── config.py          # 配置类定义
└── docker-compose.yml # Docker Compose 配置
```

## 🔧 设置步骤

### 1. 创建配置文件

```bash
# 复制模板文件
cp config/.env.example config/.env

# 编辑配置文件
nano config/.env
# 或者
vim config/.env
```

### 2. 配置必需的环境变量

编辑 `config/.env` 文件，设置以下必需的变量：

```bash
# 应用端口
APP_PORT=8001

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# API Key 认证配置
ENABLE_API_KEY_AUTH=true
API_KEYS=your-api-key-1,your-api-key-2

# MySQL 数据库配置
VANNA_MYSQL_HOST=mysql
VANNA_MYSQL_PORT=3306
VANNA_MYSQL_USER=vanna_user
VANNA_MYSQL_PASSWORD=vanna_password
VANNA_MYSQL_DATABASE=vanna_db
VANNA_MYSQL_ROOT_PASSWORD=root_password

# OpenAI/DeepSeek API 配置
VANNA_OPEN_API_KEY=your-openai-or-deepseek-api-key
VANNA_OPEN_MODEL_NAME=deepseek-chat
VANNA_OPEN_BASE_URL=https://api.deepseek.com/v1
```

### 3. 验证配置

```bash
# 检查配置文件是否存在
ls -la config/

# 检查配置文件内容 (隐藏敏感信息)
grep -v "PASSWORD\|API_KEY\|SECRET" config/.env
```

## 🐳 Docker 使用

### 1. 使用 Docker Compose

```bash
# 确保配置文件存在
ls config/.env

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f vanna-app
```

### 2. 单独使用 Docker

```bash
# 构建镜像
docker build -t vanna-texttosql-server .

# 运行容器 (手动指定环境变量)
docker run -d \
  --name vanna-server \
  -p 8001:8001 \
  --env-file config/.env \
  -v $(pwd)/logs:/app/logs \
  vanna-texttosql-server
```

## 🔍 配置验证

### 1. 检查应用启动

```bash
# 检查健康状态
curl http://localhost:8001/health

# 检查 API 信息
curl http://localhost:8001/api/v1/info
```

### 2. 检查配置加载

```bash
# 进入容器检查环境变量
docker exec -it vanna-texttosql-server env | grep -E "(VANNA|LOG|API)"

# 检查配置文件是否存在
docker exec -it vanna-texttosql-server ls -la config/
```

## 🚨 故障排除

### 常见问题

#### 1. 配置文件不存在
```bash
# 错误信息: FileNotFoundError: config/.env
# 解决方案:
cp config/.env.example config/.env
```

#### 2. 环境变量未加载
```bash
# 检查 docker-compose.yml 中的 env_file 配置
grep -A 5 "env_file" docker-compose.yml

# 确保路径正确
ls -la config/.env
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la config/

# 修复权限 (如果需要)
chmod 644 config/.env
```

### 调试命令

```bash
# 检查配置加载
python -c "
from config import Config
import os
print('Config loaded successfully')
print('LOG_LEVEL:', os.environ.get('LOG_LEVEL'))
print('APP_PORT:', os.environ.get('APP_PORT'))
"

# 检查 dotenv 加载
python -c "
from dotenv import load_dotenv
import os
load_dotenv(dotenv_path='config/.env')
print('Environment variables loaded')
print('Available vars:', [k for k in os.environ.keys() if 'VANNA' in k or 'LOG' in k])
"
```

## 📋 配置文件模板

### config/.env 完整示例

```bash
# ===========================================
# Vanna Text-to-SQL 服务器配置
# ===========================================

# 应用基础配置
APP_PORT=8001
SECRET_KEY=your-very-secret-key-change-this-in-production

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# API Key 认证配置
ENABLE_API_KEY_AUTH=true
API_KEYS=vanna-key-1,vanna-key-2,vanna-key-3

# MySQL 数据库配置
VANNA_MYSQL_HOST=mysql
VANNA_MYSQL_PORT=3306
VANNA_MYSQL_USER=vanna_user
VANNA_MYSQL_PASSWORD=secure_password_123
VANNA_MYSQL_DATABASE=vanna_db
VANNA_MYSQL_ROOT_PASSWORD=root_secure_password_456

# OpenAI/DeepSeek API 配置
VANNA_OPEN_API_KEY=sk-your-api-key-here
VANNA_OPEN_MODEL_NAME=deepseek-chat
VANNA_OPEN_BASE_URL=https://api.deepseek.com/v1

# Flask 配置
FLASK_ENV=production
FLASK_DEBUG=False
```

## 🔐 安全注意事项

### 1. 保护配置文件
```bash
# 设置适当的文件权限
chmod 600 config/.env

# 确保不提交到版本控制
echo "config/.env" >> .gitignore
```

### 2. 使用强密码
- MySQL 密码应该包含大小写字母、数字和特殊字符
- API Key 应该使用官方提供的完整密钥
- SECRET_KEY 应该是随机生成的长字符串

### 3. 环境分离
```bash
# 开发环境
cp config/.env.example config/.env.dev

# 生产环境
cp config/.env.example config/.env.prod

# 使用不同的配置文件
docker-compose --env-file config/.env.dev up -d
```

## 📚 相关文档

- [Docker Compose 环境变量文档](https://docs.docker.com/compose/environment-variables/)
- [Python dotenv 文档](https://python-dotenv.readthedocs.io/)
- [Flask 配置文档](https://flask.palletsprojects.com/en/2.3.x/config/)

## 🔄 迁移指南

### 从根目录 .env 迁移

如果你之前使用的是根目录的 `.env` 文件：

```bash
# 1. 移动现有配置文件
mv .env config/.env

# 2. 更新 docker-compose.yml (已完成)
# 3. 重新构建和启动
docker-compose down
docker-compose build
docker-compose up -d
```

### 验证迁移成功

```bash
# 检查应用是否正常启动
curl http://localhost:8001/health

# 检查配置是否正确加载
docker-compose logs vanna-app | grep -i "config\|environment"
```

## 总结

通过将配置文件移动到 `config/` 目录：

- ✅ **更好的项目组织** - 配置文件集中管理
- ✅ **清晰的结构** - 分离配置和代码
- ✅ **环境隔离** - 支持多环境配置
- ✅ **安全性提升** - 更容易管理敏感信息
- ✅ **维护便利** - 配置变更更加直观

现在你可以在 `config/.env` 文件中管理所有的环境变量配置了！
