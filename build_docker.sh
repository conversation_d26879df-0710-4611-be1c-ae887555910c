#!/bin/bash

# Docker 构建脚本
# 用于构建包含 ONNX 模型的 Vanna Text-to-SQL 服务器

set -e  # 遇到错误时退出

echo "🐳 开始构建 Vanna Text-to-SQL Docker 镜像"
echo "="*50

# 检查 onnx.tar.gz 文件是否存在
if [ ! -f "onnx.tar.gz" ]; then
    echo "❌ 错误: 找不到 onnx.tar.gz 文件"
    echo "请确保 onnx.tar.gz 文件在当前目录中"
    exit 1
fi

echo "✅ 找到 onnx.tar.gz 文件"

# 获取文件大小
file_size=$(du -h onnx.tar.gz | cut -f1)
echo "📦 ONNX 模型文件大小: $file_size"

# 设置镜像名称和标签
IMAGE_NAME="vanna-texttosql-server"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"

echo "🏗️  开始构建镜像: $FULL_IMAGE_NAME"

# 构建 Docker 镜像
docker build \
    --tag "$FULL_IMAGE_NAME" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    .

if [ $? -eq 0 ]; then
    echo "✅ Docker 镜像构建成功!"
    echo "📋 镜像信息:"
    docker images | grep "$IMAGE_NAME"
    
    echo ""
    echo "🚀 运行容器示例:"
    echo "docker run -d \\"
    echo "  --name vanna-server \\"
    echo "  -p 8000:8000 \\"
    echo "  -e VANNA_MYSQL_HOST=your-mysql-host \\"
    echo "  -e VANNA_MYSQL_USER=your-mysql-user \\"
    echo "  -e VANNA_MYSQL_PASSWORD=your-mysql-password \\"
    echo "  -e VANNA_MYSQL_DATABASE=your-database \\"
    echo "  -e API_KEYS=your-api-key-1,your-api-key-2 \\"
    echo "  -v \$(pwd)/data:/app/data \\"
    echo "  -v \$(pwd)/logs:/app/logs \\"
    echo "  $FULL_IMAGE_NAME"
    
    echo ""
    echo "🔧 开发模式运行:"
    echo "docker run -it --rm \\"
    echo "  -p 8000:8000 \\"
    echo "  -v \$(pwd):/app \\"
    echo "  $FULL_IMAGE_NAME bash"
    
else
    echo "❌ Docker 镜像构建失败!"
    exit 1
fi

echo ""
echo "📁 构建过程说明:"
echo "1. ✅ 复制 onnx.tar.gz 到容器临时目录"
echo "2. ✅ 解压到 /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/"
echo "3. ✅ 创建数据目录结构 (csv, png, images, charts)"
echo "4. ✅ 清理临时文件"
echo ""
echo "🎉 构建完成!"
