import os
from dotenv import load_dotenv

# 加载环境变量从 config/.env
load_dotenv(dotenv_path='config/.env')

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key')
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_DIR = os.environ.get('LOG_DIR', '/app/logs')

    # API Key 认证配置
    ENABLE_API_KEY_AUTH = os.environ.get('ENABLE_API_KEY_AUTH', 'True').lower() == 'true'
    API_KEYS = os.environ.get('API_KEYS', '').split(',') if os.environ.get('API_KEYS') else []

    # MySQL配置
    VANNA_MYSQL_CONFIG = {
        'host': os.environ.get('VANNA_MYSQL_HOST', 'localhost'),
        'port': int(os.environ.get('VANNA_MYSQL_PORT', 3306)),
        'user': os.environ.get('VANNA_MYSQL_USER', 'root'),
        'password': os.environ.get('VANNA_MYSQL_PASSWORD', ''),
        'database': os.environ.get('VANNA_MYSQL_DATABASE', 'test_db')
    }

    # Vanna.ai配置
    VANNA_OPEN_API_KEY = os.environ.get('VANNA_OPEN_API_KEY')
    VANNA_OPEN_MODEL_NAME = os.environ.get('VANNA_OPEN_MODEL_NAME', 'deepseek-chat')
    VANNA_OPEN_BASE_URL = os.environ.get('VANNA_OPEN_BASE_URL', 'https://api.deepseek.com/v1')