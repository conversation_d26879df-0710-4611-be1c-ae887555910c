import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key')
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    # MySQL配置
    MYSQL_CONFIG = {
        'host': os.environ.get('MYSQL_HOST', 'localhost'),
        'port': int(os.environ.get('MYSQL_PORT', 3306)),
        'user': os.environ.get('MYSQL_USER', 'root'),
        'password': os.environ.get('MYSQL_PASSWORD', ''),
        'database': os.environ.get('MYSQL_DATABASE', 'test_db')
    }

    # Vanna.ai配置
    OPEN_API_KEY = os.environ.get('OPEN_API_KEY')
    OPEN_MODEL_NAME = os.environ.get('OPEN_MODEL_NAME', 'deepseek-chat')
    OPEN_BASE_URL = os.environ.get('OPEN_BASE_URL', 'https://api.deepseek.com/v1')