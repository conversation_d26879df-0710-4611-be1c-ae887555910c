#!/usr/bin/env python3
"""
测试训练数据获取修复
验证 DataFrame 处理是否正确
"""
import requests
import json
import sys


def test_get_training_data_api():
    """测试获取训练数据 API"""
    print("🧪 测试获取训练数据 API")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8001"
    api_key = "your-api-key-here"  # 请替换为实际的 API Key
    
    # 请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"📋 发送请求到: {base_url}/api/v1/training-data")
    
    try:
        # 发送请求
        response = requests.get(
            f"{base_url}/api/v1/training-data",
            headers=headers,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON 解析成功")
                
                # 检查响应结构
                if 'success' in data and data['success']:
                    print(f"✅ API 调用成功")
                    
                    if 'data' in data:
                        api_data = data['data']
                        
                        # 检查训练数据
                        if 'training_data' in api_data:
                            training_data = api_data['training_data']
                            count = api_data.get('count', 0)
                            
                            print(f"📊 训练数据统计:")
                            print(f"   数量: {count}")
                            print(f"   类型: {type(training_data)}")
                            
                            if isinstance(training_data, list):
                                print(f"   ✅ 训练数据是列表格式")
                                
                                if len(training_data) > 0:
                                    print(f"   📋 前3条数据:")
                                    for i, item in enumerate(training_data[:3], 1):
                                        print(f"      {i}. 类型: {type(item)}")
                                        if isinstance(item, dict):
                                            print(f"         键: {list(item.keys())}")
                                            if 'id' in item:
                                                print(f"         ID: {item['id']}")
                                            if 'training_data_type' in item:
                                                print(f"         类型: {item['training_data_type']}")
                                        else:
                                            print(f"         内容: {str(item)[:100]}...")
                                else:
                                    print(f"   ℹ️  暂无训练数据")
                            else:
                                print(f"   ❌ 训练数据不是列表格式: {type(training_data)}")
                                print(f"   内容: {str(training_data)[:200]}...")
                        else:
                            print(f"   ❌ 响应中缺少 training_data 字段")
                        
                        # 检查 MySQL 配置
                        if 'mysql_config' in api_data:
                            mysql_config = api_data['mysql_config']
                            print(f"   🗄️  MySQL 配置: {mysql_config}")
                        
                    else:
                        print(f"   ❌ 响应中缺少 data 字段")
                else:
                    print(f"   ❌ API 调用失败")
                    print(f"   错误信息: {data.get('message', 'N/A')}")
                    print(f"   错误代码: {data.get('error_code', 'N/A')}")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {str(e)}")
                print(f"响应内容: {response.text[:500]}...")
                return False
                
        elif response.status_code == 401:
            print(f"❌ 认证失败 - 请检查 API Key")
            print(f"响应: {response.text}")
            return False
            
        elif response.status_code == 500:
            print(f"❌ 服务器内部错误")
            print(f"响应: {response.text}")
            
            # 尝试解析错误信息
            try:
                error_data = response.json()
                print(f"错误详情: {error_data.get('message', 'N/A')}")
                print(f"错误代码: {error_data.get('error_code', 'N/A')}")
            except:
                pass
            
            return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败 - 服务器可能未运行")
        print(f"请确保服务器在 {base_url} 上运行")
        return False
        
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
        return False
        
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dataframe_handling():
    """测试 DataFrame 处理逻辑"""
    print(f"\n" + "="*50)
    print("🔬 测试 DataFrame 处理逻辑")
    print("="*50)
    
    try:
        import pandas as pd
        
        # 模拟不同类型的训练数据
        test_cases = [
            {
                "name": "空 DataFrame",
                "data": pd.DataFrame(),
                "expected": []
            },
            {
                "name": "有数据的 DataFrame",
                "data": pd.DataFrame([
                    {"id": "1", "type": "ddl", "content": "CREATE TABLE test"},
                    {"id": "2", "type": "sql", "content": "SELECT * FROM test"}
                ]),
                "expected": [
                    {"id": "1", "type": "ddl", "content": "CREATE TABLE test"},
                    {"id": "2", "type": "sql", "content": "SELECT * FROM test"}
                ]
            },
            {
                "name": "None 值",
                "data": None,
                "expected": []
            },
            {
                "name": "空列表",
                "data": [],
                "expected": []
            },
            {
                "name": "非空列表",
                "data": [{"id": "1", "type": "ddl"}],
                "expected": [{"id": "1", "type": "ddl"}]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试: {test_case['name']}")
            
            data = test_case['data']
            expected = test_case['expected']
            
            # 模拟处理逻辑
            if data is not None:
                if hasattr(data, 'empty'):
                    # DataFrame
                    if data.empty:
                        result = []
                    else:
                        result = data.to_dict('records')
                elif isinstance(data, list):
                    result = data
                else:
                    result = [data] if data else []
            else:
                result = []
            
            print(f"   输入类型: {type(data)}")
            print(f"   输出类型: {type(result)}")
            print(f"   输出长度: {len(result)}")
            
            if result == expected:
                print(f"   ✅ 处理正确")
            else:
                print(f"   ❌ 处理错误")
                print(f"   期望: {expected}")
                print(f"   实际: {result}")
        
        return True
        
    except ImportError:
        print(f"❌ pandas 未安装，跳过 DataFrame 测试")
        return True
    except Exception as e:
        print(f"❌ DataFrame 测试异常: {str(e)}")
        return False


def show_troubleshooting():
    """显示故障排除指南"""
    print(f"\n" + "="*50)
    print("🔧 故障排除指南")
    print("="*50)
    
    print(f"\n1. 常见错误及解决方案:")
    
    print(f"\n   错误: 'DataFrame is ambiguous'")
    print(f"   原因: 直接对 DataFrame 进行布尔判断")
    print(f"   解决: 使用 df.empty 检查是否为空")
    
    print(f"\n   错误: 'ConnectionError'")
    print(f"   原因: 服务器未运行或地址错误")
    print(f"   解决: 检查服务器状态和地址")
    
    print(f"\n   错误: '401 Unauthorized'")
    print(f"   原因: API Key 无效或缺失")
    print(f"   解决: 检查 API Key 配置")
    
    print(f"\n2. 检查步骤:")
    print(f"   1. 确认服务器运行: curl http://localhost:8001/health")
    print(f"   2. 检查 API Key: 查看 config/.env 文件")
    print(f"   3. 查看服务器日志: docker-compose logs -f vanna-app")
    print(f"   4. 测试基本接口: curl http://localhost:8001/api/v1/info")
    
    print(f"\n3. 调试命令:")
    print(f"   # 查看容器状态")
    print(f"   docker-compose ps")
    print(f"   ")
    print(f"   # 查看应用日志")
    print(f"   docker-compose logs --tail=50 vanna-app")
    print(f"   ")
    print(f"   # 进入容器调试")
    print(f"   docker exec -it vanna-texttosql-server bash")


def main():
    """主测试函数"""
    print("🚀 训练数据获取修复测试")
    print("="*50)
    
    print(f"\n⚠️  注意事项:")
    print(f"   1. 请确保服务器正在运行")
    print(f"   2. 请在脚本中设置正确的 API Key")
    print(f"   3. 请确保有有效的 MySQL 连接")
    
    # 测试 DataFrame 处理逻辑
    dataframe_success = test_dataframe_handling()
    
    # 测试 API 接口
    api_success = test_get_training_data_api()
    
    # 显示故障排除指南
    show_troubleshooting()
    
    # 显示测试结果
    print(f"\n" + "="*50)
    print("📊 测试结果")
    print("="*50)
    
    print(f"DataFrame 处理测试: {'✅ 通过' if dataframe_success else '❌ 失败'}")
    print(f"API 接口测试: {'✅ 通过' if api_success else '❌ 失败'}")
    
    if dataframe_success and api_success:
        print(f"\n🎉 所有测试通过! DataFrame 错误已修复。")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查上述故障排除指南。")
        return False


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试脚本错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
