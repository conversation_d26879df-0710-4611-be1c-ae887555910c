#!/usr/bin/env python3
"""
API Key 认证测试脚本
测试不同的认证场景
"""
import requests
import json
import time


class APIAuthTester:
    def __init__(self, base_url="http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.test_results = []
    
    def test_request(self, endpoint, method="GET", headers=None, data=None, expected_status=200):
        """执行测试请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data, timeout=10)
            else:
                response = requests.get(url, headers=headers, timeout=10)
            
            success = response.status_code == expected_status
            result = {
                'endpoint': endpoint,
                'method': method,
                'headers': headers or {},
                'status_code': response.status_code,
                'expected_status': expected_status,
                'success': success,
                'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            }
            
            self.test_results.append(result)
            return result
            
        except Exception as e:
            result = {
                'endpoint': endpoint,
                'method': method,
                'headers': headers or {},
                'error': str(e),
                'success': False
            }
            self.test_results.append(result)
            return result
    
    def test_no_auth(self):
        """测试无认证访问"""
        print("🔍 测试无认证访问...")
        
        # 测试受保护的端点
        result1 = self.test_request("/train", "POST", expected_status=401)
        result2 = self.test_request("/ask", "POST", expected_status=401)
        
        # 测试可选认证的端点
        result3 = self.test_request("/auth/info", "GET", expected_status=200)
        
        print(f"  训练端点 (无认证): {'✅' if result1['success'] else '❌'} - {result1.get('status_code', 'Error')}")
        print(f"  查询端点 (无认证): {'✅' if result2['success'] else '❌'} - {result2.get('status_code', 'Error')}")
        print(f"  认证信息 (无认证): {'✅' if result3['success'] else '❌'} - {result3.get('status_code', 'Error')}")
    
    def test_invalid_auth(self):
        """测试无效认证"""
        print("\n🔍 测试无效认证...")
        
        invalid_keys = [
            "invalid-key",
            "short",
            "very-long-but-invalid-key-that-should-not-work-at-all",
            ""
        ]
        
        for i, key in enumerate(invalid_keys):
            headers = {"X-API-Key": key}
            result = self.test_request("/train", "POST", headers=headers, expected_status=401)
            print(f"  无效密钥 {i+1}: {'✅' if result['success'] else '❌'} - {result.get('status_code', 'Error')}")
    
    def test_valid_auth(self, api_key):
        """测试有效认证"""
        print(f"\n🔍 测试有效认证 (密钥: {api_key[:8]}...)...")
        
        # 测试不同的认证头格式
        auth_headers = [
            {"X-API-Key": api_key},
            {"API-Key": api_key},
            {"Authorization": f"Bearer {api_key}"}
        ]
        
        for i, headers in enumerate(auth_headers):
            result = self.test_request("/auth/info", "GET", headers=headers, expected_status=200)
            header_type = list(headers.keys())[0]
            print(f"  {header_type} 头: {'✅' if result['success'] else '❌'} - {result.get('status_code', 'Error')}")
    
    def test_api_endpoints(self, api_key):
        """测试 API 端点功能"""
        print(f"\n🔍 测试 API 端点功能...")
        
        headers = {"X-API-Key": api_key}
        
        # 测试验证端点
        validate_data = {"api_key": api_key}
        result1 = self.test_request("/auth/validate-key", "POST", headers=None, data=validate_data, expected_status=200)
        print(f"  验证密钥端点: {'✅' if result1['success'] else '❌'} - {result1.get('status_code', 'Error')}")
        
        # 测试生成新密钥端点
        generate_data = {"length": 24}
        result2 = self.test_request("/auth/generate-key", "POST", headers=headers, data=generate_data, expected_status=200)
        print(f"  生成密钥端点: {'✅' if result2['success'] else '❌'} - {result2.get('status_code', 'Error')}")
        
        # 测试训练端点（只测试认证，不提供实际数据）
        train_data = {}
        result3 = self.test_request("/train", "POST", headers=headers, data=train_data, expected_status=400)  # 期望参数错误
        print(f"  训练端点 (认证): {'✅' if result3['success'] else '❌'} - {result3.get('status_code', 'Error')}")
        
        # 测试查询端点（只测试认证，不提供实际数据）
        ask_data = {}
        result4 = self.test_request("/ask", "POST", headers=headers, data=ask_data, expected_status=400)  # 期望参数错误
        print(f"  查询端点 (认证): {'✅' if result4['success'] else '❌'} - {result4.get('status_code', 'Error')}")
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📊 测试摘要")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")
        
        # 显示失败的测试
        failed_tests = [result for result in self.test_results if not result.get('success', False)]
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test['method']} {test['endpoint']}: {test.get('error', f'状态码 {test.get(\"status_code\", \"未知\")}')}") 


def main():
    print("🚀 API Key 认证测试")
    print("="*60)
    
    # 提示用户输入测试用的 API Key
    print("请提供一个有效的 API Key 进行测试:")
    print("(如果没有，请先运行: python generate_api_keys.py generate)")
    api_key = input("API Key: ").strip()
    
    if not api_key:
        print("❌ 未提供 API Key，将只测试无认证和无效认证场景")
        api_key = None
    
    tester = APIAuthTester()
    
    # 执行测试
    tester.test_no_auth()
    tester.test_invalid_auth()
    
    if api_key:
        tester.test_valid_auth(api_key)
        tester.test_api_endpoints(api_key)
    
    tester.print_summary()
    
    print(f"\n💡 提示:")
    print(f"  - 确保服务器运行在 http://localhost:8000")
    print(f"  - 确保在 .env 文件中配置了有效的 API_KEYS")
    print(f"  - 使用 'python generate_api_keys.py generate' 生成新的 API Keys")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试错误: {str(e)}")
