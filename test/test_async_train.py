#!/usr/bin/env python3
"""
测试异步训练功能
"""
import requests
import json
import time

def test_async_train():
    """测试异步训练API"""
    url = "http://localhost:8000/api/v1/train"
    
    # 测试数据
    test_data = {
        "ddl": "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(100));",
        "documentation": "用户表包含用户的基本信息",
        "sql": "SELECT * FROM users WHERE id = 1;",
        "question_sql_pairs": [
            {
                "question": "查询用户ID为1的用户信息",
                "sql": "SELECT * FROM users WHERE id = 1;"
            }
        ]
    }
    
    print("发送训练请求...")
    start_time = time.time()
    
    try:
        response = requests.post(url, json=test_data, timeout=5)
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f} 秒")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and "后台执行" in result.get('message', ''):
                print("✅ 异步训练功能正常工作！")
                return True
            else:
                print("❌ 响应格式不正确")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时（超过5秒），异步功能可能有问题")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试异步训练功能...")
    success = test_async_train()
    
    if success:
        print("\n🎉 测试通过！训练请求能够立即返回，训练在后台执行。")
    else:
        print("\n💥 测试失败！请检查服务器状态和配置。")
