# MySQL 默认配置功能总结

## 功能概述

`train`、`ask` 和 `mysql/test-connection` 接口的 MySQL 参数都是**完全可选的**。当不传入 MySQL 参数时，系统会自动使用环境变量（`.env` 文件）中的默认配置。

## 核心特性

### ✅ 完全可选
- 所有 MySQL 参数都是可选的
- 不传任何参数时，使用环境变量配置
- 简化了大部分使用场景

### ✅ 智能合并
- 请求参数优先于环境变量
- 可以只传部分参数
- 未传的参数自动使用默认值

### ✅ 灵活配置
- 支持完全默认配置
- 支持部分覆盖配置
- 支持完全自定义配置

## 使用方式

### 1. 完全使用默认配置（推荐）

```bash
# 训练接口
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100));",
    "documentation": "用户表"
  }'

# 查询接口
curl -X POST http://localhost:8000/api/v1/ask \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户"
  }'

# 连接测试
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 部分覆盖配置

```bash
# 只改变主机和数据库，其他使用默认
curl -X POST http://localhost:8000/api/v1/ask \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户",
    "mysql_host": "production-db.company.com",
    "mysql_database": "prod_db"
  }'
```

### 3. 完全自定义配置

```bash
# 传入所有 MySQL 参数
curl -X POST http://localhost:8000/api/v1/ask \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户",
    "mysql_host": "custom-db.company.com",
    "mysql_port": 3306,
    "mysql_user": "custom_user",
    "mysql_password": "custom_password",
    "mysql_database": "custom_db"
  }'
```

## 环境变量配置

在 `.env` 文件中配置默认的 MySQL 连接参数：

```bash
# MySQL 默认配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_default_db
```

## 参数优先级

1. **请求参数** - 在 API 请求中传入的参数
2. **环境变量** - `.env` 文件中的配置
3. **内置默认值** - 如 `mysql_port` 默认为 3306

## 配置验证

系统会验证最终的配置必须包含以下必需参数：
- `host` - MySQL 主机地址
- `user` - MySQL 用户名  
- `database` - MySQL 数据库名

这些参数可以来自请求参数或环境变量。

## 使用场景

### 开发环境
```bash
# .env 配置
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_DATABASE=dev_db

# 使用时不传任何 MySQL 参数
```

### 测试环境
```bash
# 只传数据库名，其他使用默认
{
  "question": "查询测试数据",
  "mysql_database": "test_db"
}
```

### 生产环境
```bash
# 传入生产环境的完整配置
{
  "question": "查询生产数据",
  "mysql_host": "prod-db.company.com",
  "mysql_user": "prod_user",
  "mysql_password": "prod_password",
  "mysql_database": "prod_db"
}
```

### 多租户系统
```bash
# 为不同客户使用不同数据库
{
  "question": "查询客户数据",
  "mysql_host": "tenant-a-db.company.com",
  "mysql_database": "tenant_a_data"
}
```

## 响应信息

所有接口都会在响应中返回实际使用的 MySQL 配置（不包含密码）：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "root",
      "database": "your_db"
    },
    // ... 其他响应数据
  }
}
```

## 测试工具

提供了以下测试脚本来验证默认配置功能：

- `test_default_mysql_config.py` - 全面测试默认配置功能
- `demo_default_config.py` - 交互式演示默认配置使用

```bash
# 运行测试
python test_default_mysql_config.py
python demo_default_config.py
```

## 最佳实践

1. **配置默认值** - 在 `.env` 文件中配置常用的数据库连接参数
2. **简化调用** - 大部分情况下不需要传 MySQL 参数
3. **按需覆盖** - 只在需要连接不同数据库时传入参数
4. **测试连接** - 使用 `mysql/test-connection` 接口验证配置

## 优势

- **简化使用** - 减少了大部分 API 调用的参数
- **提高效率** - 不需要每次都传入相同的数据库配置
- **灵活切换** - 可以轻松切换到不同的数据库
- **向后兼容** - 完全兼容之前需要传入所有参数的使用方式

## 注意事项

- 确保 `.env` 文件中配置了正确的默认 MySQL 参数
- 最终配置必须包含 `host`、`user`、`database` 三个必需参数
- 请求参数会覆盖环境变量中的相同参数
- 响应中不会返回密码等敏感信息
