#!/bin/bash

# Vanna Text-to-SQL Server 部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|build]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="vanna-texttosql-server"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在"
        if [ -f .env.example ]; then
            log_info "复制 .env.example 到 .env"
            cp .env.example .env
            log_warning "请编辑 .env 文件，设置正确的配置参数"
            log_warning "特别是 VANNA_OPEN_API_KEY 等重要配置"
        else
            log_error ".env.example 文件也不存在，请手动创建 .env 文件"
            exit 1
        fi
    else
        log_success "环境变量文件检查通过"
    fi
}

# 构建镜像
build_image() {
    log_info "构建 Docker 镜像..."
    docker-compose build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功"
        log_info "应用访问地址: http://localhost:8000"
        log_info "健康检查: http://localhost:8000/health"
        log_info "查看日志: ./deploy.sh logs"
    else
        log_error "服务启动失败，请查看日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 清理资源
cleanup() {
    log_info "清理 Docker 资源..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Vanna Text-to-SQL Server 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  build     - 构建镜像"
    echo "  logs      - 查看日志"
    echo "  cleanup   - 清理资源"
    echo "  help      - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  ./deploy.sh start"
    echo "  ./deploy.sh logs"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_dependencies
            check_env_file
            start_services
            ;;
        "stop")
            check_dependencies
            stop_services
            ;;
        "restart")
            check_dependencies
            check_env_file
            restart_services
            ;;
        "build")
            check_dependencies
            check_env_file
            build_image
            ;;
        "logs")
            check_dependencies
            show_logs
            ;;
        "cleanup")
            check_dependencies
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
