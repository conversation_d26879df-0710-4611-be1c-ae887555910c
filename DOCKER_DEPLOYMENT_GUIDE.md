# Docker 部署指南

## 概述

本指南介绍如何使用 Docker 部署 Vanna Text-to-SQL 服务器，包括 ONNX 模型的集成和完整的环境配置。

## 前置要求

### 必需文件
- ✅ `onnx.tar.gz` - ONNX 模型文件（必须放在项目根目录）
- ✅ `Dockerfile` - Docker 构建文件
- ✅ `docker-compose.yml` - Docker Compose 配置
- ✅ `.env` - 环境变量配置文件

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

## 快速开始

### 1. 准备环境文件

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 2. 确保 ONNX 模型文件存在

```bash
# 检查文件是否存在
ls -la onnx.tar.gz

# 查看文件大小
du -h onnx.tar.gz
```

### 3. 构建和启动服务

```bash
# 使用 Docker Compose 构建并启动
docker-compose up -d

# 或者使用构建脚本
chmod +x build_docker.sh
./build_docker.sh
```

### 4. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f vanna-app

# 测试健康检查
curl http://localhost:8000/health
```

## 详细配置

### Dockerfile 关键特性

#### ONNX 模型集成
```dockerfile
# 创建 ONNX 模型目录
RUN mkdir -p /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/

# 复制并解压 ONNX 模型文件
COPY onnx.tar.gz /tmp/onnx.tar.gz
RUN tar -xzf /tmp/onnx.tar.gz -C /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/ && \
    rm /tmp/onnx.tar.gz
```

#### 数据目录结构
```dockerfile
# 创建数据目录
RUN mkdir -p /app/data/csv /app/data/png /app/data/images /app/data/charts
```

### 环境变量配置

#### 必需配置
```bash
# API 认证
API_KEYS=your-api-key-1,your-api-key-2

# MySQL 数据库
VANNA_MYSQL_HOST=mysql
VANNA_MYSQL_USER=vanna_user
VANNA_MYSQL_PASSWORD=vanna_password
VANNA_MYSQL_DATABASE=vanna_db

# OpenAI/DeepSeek API
VANNA_OPEN_API_KEY=your-api-key
VANNA_OPEN_MODEL_NAME=deepseek-chat
VANNA_OPEN_BASE_URL=https://api.deepseek.com/v1
```

#### 可选配置
```bash
# 应用配置
APP_PORT=8000
FLASK_ENV=production
LOG_LEVEL=INFO

# MySQL Root 密码
VANNA_MYSQL_ROOT_PASSWORD=root_password
```

## 部署方式

### 方式一：Docker Compose（推荐）

```bash
# 启动所有服务（包括 MySQL）
docker-compose up -d

# 只启动应用服务（使用外部 MySQL）
docker-compose up -d vanna-app

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### 方式二：单独 Docker 容器

```bash
# 构建镜像
docker build -t vanna-texttosql-server .

# 运行容器
docker run -d \
  --name vanna-server \
  -p 8000:8000 \
  -e VANNA_MYSQL_HOST=your-mysql-host \
  -e VANNA_MYSQL_USER=your-mysql-user \
  -e VANNA_MYSQL_PASSWORD=your-mysql-password \
  -e VANNA_MYSQL_DATABASE=your-database \
  -e API_KEYS=your-api-key-1,your-api-key-2 \
  -e VANNA_OPEN_API_KEY=your-openai-key \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  vanna-texttosql-server
```

## 数据持久化

### 卷挂载说明

```yaml
volumes:
  # 应用数据（CSV、图片文件）
  - ./data:/app/data
  
  # 应用日志
  - ./logs:/app/logs
  
  # ChromaDB 数据
  - ./chromadb:/app/chromadb
  
  # MySQL 数据（仅使用内置 MySQL 时）
  - VANNA_MYSQL_data:/var/lib/mysql
```

### 目录结构

```
project/
├── data/                 # 数据文件目录
│   ├── csv/             # CSV 文件
│   ├── png/             # PNG 图片
│   ├── images/          # 其他图片
│   └── charts/          # 图表文件
├── logs/                # 应用日志
├── chromadb/            # ChromaDB 数据
└── sql/                 # MySQL 初始化脚本
```

## 监控和维护

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8000/health

# 检查 API 可用性
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/api/v1/files/list
```

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f vanna-app

# 查看 MySQL 日志
docker-compose logs -f mysql

# 查看特定时间的日志
docker-compose logs --since="2023-12-01T10:00:00" vanna-app
```

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看容器详细信息
docker inspect vanna-texttosql-server
```

## 故障排除

### 常见问题

#### 1. ONNX 模型文件问题
```bash
# 检查文件是否存在
ls -la onnx.tar.gz

# 检查文件是否被正确复制到容器
docker exec vanna-texttosql-server ls -la /root/.cache/chroma/onnx_models/all-MiniLM-L6-v2/
```

#### 2. 数据库连接问题
```bash
# 检查 MySQL 服务状态
docker-compose ps mysql

# 测试数据库连接
docker exec vanna-texttosql-server mysql -h mysql -u vanna_user -p
```

#### 3. API 认证问题
```bash
# 检查环境变量
docker exec vanna-texttosql-server env | grep API_KEYS

# 测试 API 访问
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/api/v1/auth/info
```

### 调试模式

```bash
# 以交互模式运行容器
docker run -it --rm \
  -p 8000:8000 \
  -v $(pwd):/app \
  vanna-texttosql-server bash

# 在运行的容器中执行命令
docker exec -it vanna-texttosql-server bash
```

## 生产部署建议

### 安全配置
- 🔒 使用强密码和复杂的 API Keys
- 🔒 限制容器网络访问
- 🔒 定期更新基础镜像
- 🔒 使用 secrets 管理敏感信息

### 性能优化
- 🚀 配置适当的资源限制
- 🚀 使用 SSD 存储
- 🚀 配置日志轮转
- 🚀 监控内存和 CPU 使用情况

### 备份策略
- 💾 定期备份数据卷
- 💾 备份环境配置文件
- 💾 备份 MySQL 数据库
- 💾 版本控制配置文件

## 更新和维护

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

### 数据备份

```bash
# 备份 MySQL 数据
docker exec vanna-mysql mysqldump -u root -p vanna_db > backup.sql

# 备份应用数据
tar -czf data-backup.tar.gz data/ logs/ chromadb/
```

### 清理资源

```bash
# 清理未使用的镜像
docker image prune

# 清理未使用的卷
docker volume prune

# 完全清理（谨慎使用）
docker system prune -a
```

## 总结

通过本指南，你可以：
- ✅ 成功部署包含 ONNX 模型的 Vanna 服务器
- ✅ 配置完整的数据库和文件管理功能
- ✅ 实现数据持久化和服务监控
- ✅ 处理常见的部署问题

如有问题，请检查日志文件或参考故障排除部分。
