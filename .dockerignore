# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore

# Logs
*.log
logs/

# Test files
test/
tests/
*_test.py
test_*.py

# Documentation
README.md
*.md

# Environment files (these should be provided at runtime)
.env
.env.local
.env.*.local

# ChromaDB data (will be persisted via volumes)
*.sqlite3
chroma.sqlite3

# Temporary files
tmp/
temp/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# PyCharm
.idea/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version
