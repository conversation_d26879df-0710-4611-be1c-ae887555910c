# 文件管理接口指南

## 概述

新增的文件管理接口允许用户获取、下载和预览存储在 `data` 目录中的 CSV 文件和图片文件。这些接口为调用方提供了便捷的文件访问功能。

## 接口列表

| 接口 | 方法 | 认证 | 功能 |
|------|------|------|------|
| `/api/v1/files/list` | GET | 需要 | 获取文件列表 |
| `/api/v1/files/download/<filename>` | GET | 需要 | 下载文件 |
| `/api/v1/files/preview/<filename>` | GET | 需要 | 预览文件（主要用于图片） |

## 1. 获取文件列表

### 接口信息
- **路径**: `GET /api/v1/files/list`
- **认证**: 需要 API Key
- **功能**: 获取 data 目录中的所有 CSV 和图片文件列表

### 请求示例
```bash
curl -X GET http://localhost:8000/api/v1/files/list \
  -H "Authorization: Bearer your-api-key"
```

### 响应格式
```json
{
  "success": true,
  "message": "文件列表获取成功",
  "data": {
    "data_directory": "/path/to/data",
    "total_files": 5,
    "csv_count": 2,
    "image_count": 3,
    "csv_files": [
      {
        "filename": "users.csv",
        "size": 1024,
        "size_human": "1.0 KB",
        "modified_time": "2023-12-01T10:30:00",
        "download_url": "/api/v1/files/download/users.csv",
        "extension": ".csv"
      }
    ],
    "image_files": [
      {
        "filename": "chart.png",
        "size": 2048,
        "size_human": "2.0 KB",
        "modified_time": "2023-12-01T11:00:00",
        "download_url": "/api/v1/files/download/chart.png",
        "preview_url": "/api/v1/files/preview/chart.png",
        "extension": ".png"
      }
    ]
  }
}
```

### 支持的文件类型

#### CSV 文件
- `.csv`

#### 图片文件
- `.jpg`, `.jpeg`
- `.png`
- `.gif`
- `.bmp`
- `.webp`
- `.svg`

## 2. 下载文件

### 接口信息
- **路径**: `GET /api/v1/files/download/<filename>`
- **认证**: 需要 API Key
- **功能**: 下载指定的文件

### 请求示例
```bash
curl -X GET http://localhost:8000/api/v1/files/download/users.csv \
  -H "Authorization: Bearer your-api-key" \
  -o downloaded_users.csv
```

### 响应
- **成功**: 返回文件内容，浏览器会提示下载
- **失败**: 返回 JSON 错误信息

### 错误情况
- `404`: 文件不存在
- `400`: 无效的文件名（包含路径遍历字符）
- `401`: 认证失败

## 3. 预览文件

### 接口信息
- **路径**: `GET /api/v1/files/preview/<filename>`
- **认证**: 需要 API Key
- **功能**: 预览文件（主要用于图片，浏览器会直接显示）

### 请求示例
```bash
curl -X GET http://localhost:8000/api/v1/files/preview/chart.png \
  -H "Authorization: Bearer your-api-key" \
  -o preview_chart.png
```

### 在浏览器中使用
```html
<img src="http://localhost:8000/api/v1/files/preview/chart.png" 
     alt="Chart" 
     style="max-width: 100%;">
```

**注意**: 浏览器中使用时需要通过 JavaScript 添加认证头，或使用代理服务器。

## 安全特性

### 1. 路径遍历防护
- 自动检测并阻止 `..`、`/`、`\` 等路径遍历字符
- 只允许访问 `data` 目录中的文件

### 2. 文件类型验证
- 只列出支持的文件类型
- 自动检测 MIME 类型

### 3. API Key 认证
- 所有接口都需要有效的 API Key
- 支持多种认证头格式

## 使用场景

### 1. 数据导出
```python
import requests

# 获取所有 CSV 文件
response = requests.get(
    "http://localhost:8000/api/v1/files/list",
    headers={"Authorization": "Bearer your-api-key"}
)

csv_files = response.json()['data']['csv_files']

# 下载每个 CSV 文件
for csv_file in csv_files:
    download_response = requests.get(
        f"http://localhost:8000{csv_file['download_url']}",
        headers={"Authorization": "Bearer your-api-key"}
    )
    
    with open(csv_file['filename'], 'wb') as f:
        f.write(download_response.content)
```

### 2. 图片展示
```javascript
// 获取图片列表并显示
async function displayImages() {
    const response = await fetch('/api/v1/files/list', {
        headers: {
            'Authorization': 'Bearer your-api-key'
        }
    });
    
    const data = await response.json();
    const imageContainer = document.getElementById('images');
    
    data.data.image_files.forEach(image => {
        const img = document.createElement('img');
        img.src = `/api/v1/files/preview/${image.filename}`;
        img.alt = image.filename;
        imageContainer.appendChild(img);
    });
}
```

### 3. 文件管理界面
```html
<!DOCTYPE html>
<html>
<head>
    <title>文件管理</title>
</head>
<body>
    <h1>文件管理</h1>
    
    <h2>CSV 文件</h2>
    <div id="csv-files"></div>
    
    <h2>图片文件</h2>
    <div id="image-files"></div>
    
    <script>
        const API_KEY = 'your-api-key';
        const BASE_URL = 'http://localhost:8000/api/v1';
        
        async function loadFiles() {
            const response = await fetch(`${BASE_URL}/files/list`, {
                headers: {
                    'Authorization': `Bearer ${API_KEY}`
                }
            });
            
            const result = await response.json();
            if (result.success) {
                renderCSVFiles(result.data.csv_files);
                renderImageFiles(result.data.image_files);
            }
        }
        
        function renderCSVFiles(csvFiles) {
            const container = document.getElementById('csv-files');
            container.innerHTML = csvFiles.map(file => `
                <div>
                    <span>${file.filename} (${file.size_human})</span>
                    <button onclick="downloadFile('${file.filename}')">下载</button>
                </div>
            `).join('');
        }
        
        function renderImageFiles(imageFiles) {
            const container = document.getElementById('image-files');
            container.innerHTML = imageFiles.map(file => `
                <div>
                    <img src="${BASE_URL}/files/preview/${file.filename}" 
                         style="max-width: 200px; max-height: 200px;">
                    <p>${file.filename} (${file.size_human})</p>
                    <button onclick="downloadFile('${file.filename}')">下载</button>
                </div>
            `).join('');
        }
        
        async function downloadFile(filename) {
            const response = await fetch(`${BASE_URL}/files/download/${filename}`, {
                headers: {
                    'Authorization': `Bearer ${API_KEY}`
                }
            });
            
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }
        
        // 页面加载时获取文件列表
        loadFiles();
    </script>
</body>
</html>
```

## 响应字段说明

### 文件信息字段
| 字段 | 类型 | 说明 |
|------|------|------|
| `filename` | string | 文件名 |
| `size` | integer | 文件大小（字节） |
| `size_human` | string | 人类可读的文件大小 |
| `modified_time` | string | 文件修改时间（ISO 格式） |
| `download_url` | string | 下载链接 |
| `preview_url` | string | 预览链接（仅图片文件） |
| `extension` | string | 文件扩展名 |

### 列表响应字段
| 字段 | 类型 | 说明 |
|------|------|------|
| `data_directory` | string | 数据目录路径 |
| `total_files` | integer | 总文件数 |
| `csv_count` | integer | CSV 文件数量 |
| `image_count` | integer | 图片文件数量 |
| `csv_files` | array | CSV 文件列表 |
| `image_files` | array | 图片文件列表 |

## 测试工具

提供了以下测试脚本：

- `test_file_management.py` - 基础功能测试
- `demo_file_integration.py` - 集成使用演示

```bash
# 运行测试
python test_file_management.py
python demo_file_integration.py
```

## 注意事项

1. **文件路径**: 所有文件都存储在项目根目录的 `data` 文件夹中
2. **文件大小**: 没有文件大小限制，但建议合理控制
3. **并发访问**: 支持多用户同时访问
4. **缓存**: 浏览器可能会缓存图片，可以添加版本参数避免缓存
5. **认证**: 所有接口都需要有效的 API Key

## 错误处理

常见错误及解决方案：

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| 401 | 认证失败 | 检查 API Key 是否正确 |
| 404 | 文件不存在 | 确认文件名和路径正确 |
| 400 | 无效文件名 | 检查文件名是否包含非法字符 |
| 500 | 服务器错误 | 检查服务器日志 |
