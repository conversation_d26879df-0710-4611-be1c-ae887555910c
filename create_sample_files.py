#!/usr/bin/env python3
"""
创建示例文件用于测试文件管理接口
"""
import os
import csv
import json
from datetime import datetime, timedelta
import random


def create_data_directory():
    """创建 data 目录"""
    data_dir = "data"
    os.makedirs(data_dir, exist_ok=True)
    print(f"✅ 创建/确认 data 目录: {data_dir}")
    return data_dir


def create_sample_csv_files(data_dir):
    """创建示例 CSV 文件"""
    print(f"\n📊 创建示例 CSV 文件...")
    
    # 1. 用户数据 CSV
    users_data = [
        ["id", "name", "email", "age", "department", "salary"],
        [1, "张三", "<EMAIL>", 28, "技术部", 15000],
        [2, "李四", "<EMAIL>", 32, "销售部", 12000],
        [3, "王五", "<EMAIL>", 29, "技术部", 16000],
        [4, "赵六", "z<PERSON><PERSON><PERSON>@company.com", 35, "人事部", 11000],
        [5, "钱七", "<EMAIL>", 26, "市场部", 9000],
    ]
    
    users_file = os.path.join(data_dir, "users.csv")
    with open(users_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(users_data)
    print(f"  ✅ 创建用户数据: {users_file}")
    
    # 2. 销售数据 CSV
    sales_data = [["date", "product", "quantity", "revenue"]]
    
    # 生成30天的销售数据
    base_date = datetime.now() - timedelta(days=30)
    products = ["产品A", "产品B", "产品C", "产品D", "产品E"]
    
    for i in range(30):
        date = (base_date + timedelta(days=i)).strftime("%Y-%m-%d")
        for product in random.sample(products, random.randint(2, 4)):
            quantity = random.randint(10, 100)
            price = random.randint(50, 500)
            revenue = quantity * price
            sales_data.append([date, product, quantity, revenue])
    
    sales_file = os.path.join(data_dir, "sales_data.csv")
    with open(sales_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(sales_data)
    print(f"  ✅ 创建销售数据: {sales_file} ({len(sales_data)-1} 条记录)")
    
    # 3. 查询结果 CSV
    query_results = [
        ["query_id", "question", "sql", "execution_time", "result_count"],
        [1, "查询所有用户", "SELECT * FROM users", 0.05, 5],
        [2, "查询技术部员工", "SELECT * FROM users WHERE department = '技术部'", 0.03, 2],
        [3, "查询平均薪资", "SELECT AVG(salary) FROM users", 0.02, 1],
        [4, "查询各部门人数", "SELECT department, COUNT(*) FROM users GROUP BY department", 0.04, 4],
    ]
    
    queries_file = os.path.join(data_dir, "query_results.csv")
    with open(queries_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(query_results)
    print(f"  ✅ 创建查询结果: {queries_file}")


def create_sample_images(data_dir):
    """创建示例图片文件"""
    print(f"\n🖼️  创建示例图片文件...")
    
    # 1. 简单的 SVG 图表
    svg_chart = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="400" height="300" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="200" y="30" font-family="Arial" font-size="18" font-weight="bold" 
        text-anchor="middle" fill="#333">销售数据图表</text>
  
  <!-- 柱状图 -->
  <rect x="50" y="80" width="40" height="120" fill="#4CAF50"/>
  <rect x="110" y="100" width="40" height="100" fill="#2196F3"/>
  <rect x="170" y="60" width="40" height="140" fill="#FF9800"/>
  <rect x="230" y="90" width="40" height="110" fill="#9C27B0"/>
  <rect x="290" y="70" width="40" height="130" fill="#F44336"/>
  
  <!-- 标签 -->
  <text x="70" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">产品A</text>
  <text x="130" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">产品B</text>
  <text x="190" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">产品C</text>
  <text x="250" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">产品D</text>
  <text x="310" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">产品E</text>
  
  <!-- 数值 -->
  <text x="70" y="75" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">120</text>
  <text x="130" y="95" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">100</text>
  <text x="190" y="55" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">140</text>
  <text x="250" y="85" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">110</text>
  <text x="310" y="65" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">130</text>
</svg>"""
    
    chart_file = os.path.join(data_dir, "sales_chart.svg")
    with open(chart_file, 'w', encoding='utf-8') as f:
        f.write(svg_chart)
    print(f"  ✅ 创建销售图表: {chart_file}")
    
    # 2. 用户分布饼图
    pie_chart = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="300" height="300" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="150" y="25" font-family="Arial" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="#333">部门人员分布</text>
  
  <!-- 饼图 -->
  <circle cx="150" cy="150" r="80" fill="none" stroke="#ddd" stroke-width="1"/>
  
  <!-- 技术部 40% -->
  <path d="M 150 70 A 80 80 0 0 1 206.4 106.4 L 150 150 Z" fill="#4CAF50"/>
  
  <!-- 销售部 30% -->
  <path d="M 206.4 106.4 A 80 80 0 0 1 193.6 193.6 L 150 150 Z" fill="#2196F3"/>
  
  <!-- 人事部 20% -->
  <path d="M 193.6 193.6 A 80 80 0 0 1 118.4 218 L 150 150 Z" fill="#FF9800"/>
  
  <!-- 市场部 10% -->
  <path d="M 118.4 218 A 80 80 0 0 1 150 70 L 150 150 Z" fill="#9C27B0"/>
  
  <!-- 图例 -->
  <rect x="50" y="250" width="12" height="12" fill="#4CAF50"/>
  <text x="70" y="261" font-family="Arial" font-size="12" fill="#333">技术部 (40%)</text>
  
  <rect x="150" y="250" width="12" height="12" fill="#2196F3"/>
  <text x="170" y="261" font-family="Arial" font-size="12" fill="#333">销售部 (30%)</text>
  
  <rect x="50" y="270" width="12" height="12" fill="#FF9800"/>
  <text x="70" y="281" font-family="Arial" font-size="12" fill="#333">人事部 (20%)</text>
  
  <rect x="150" y="270" width="12" height="12" fill="#9C27B0"/>
  <text x="170" y="281" font-family="Arial" font-size="12" fill="#333">市场部 (10%)</text>
</svg>"""
    
    pie_file = os.path.join(data_dir, "department_distribution.svg")
    with open(pie_file, 'w', encoding='utf-8') as f:
        f.write(pie_chart)
    print(f"  ✅ 创建部门分布图: {pie_file}")
    
    # 3. 简单的数据可视化
    line_chart = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="500" height="300" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="250" y="25" font-family="Arial" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="#333">月度销售趋势</text>
  
  <!-- 坐标轴 -->
  <line x1="50" y1="50" x2="50" y2="250" stroke="#333" stroke-width="2"/>
  <line x1="50" y1="250" x2="450" y2="250" stroke="#333" stroke-width="2"/>
  
  <!-- 数据线 -->
  <polyline points="50,200 100,180 150,160 200,140 250,120 300,100 350,110 400,90 450,80" 
            fill="none" stroke="#4CAF50" stroke-width="3"/>
  
  <!-- 数据点 -->
  <circle cx="50" cy="200" r="4" fill="#4CAF50"/>
  <circle cx="100" cy="180" r="4" fill="#4CAF50"/>
  <circle cx="150" cy="160" r="4" fill="#4CAF50"/>
  <circle cx="200" cy="140" r="4" fill="#4CAF50"/>
  <circle cx="250" cy="120" r="4" fill="#4CAF50"/>
  <circle cx="300" cy="100" r="4" fill="#4CAF50"/>
  <circle cx="350" cy="110" r="4" fill="#4CAF50"/>
  <circle cx="400" cy="90" r="4" fill="#4CAF50"/>
  <circle cx="450" cy="80" r="4" fill="#4CAF50"/>
  
  <!-- 月份标签 -->
  <text x="50" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">1月</text>
  <text x="100" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">2月</text>
  <text x="150" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">3月</text>
  <text x="200" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">4月</text>
  <text x="250" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">5月</text>
  <text x="300" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">6月</text>
  <text x="350" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">7月</text>
  <text x="400" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">8月</text>
  <text x="450" y="270" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">9月</text>
</svg>"""
    
    trend_file = os.path.join(data_dir, "sales_trend.svg")
    with open(trend_file, 'w', encoding='utf-8') as f:
        f.write(line_chart)
    print(f"  ✅ 创建销售趋势图: {trend_file}")


def create_metadata_file(data_dir):
    """创建元数据文件"""
    print(f"\n📋 创建元数据文件...")
    
    metadata = {
        "created_at": datetime.now().isoformat(),
        "description": "Vanna Text-to-SQL 示例数据文件",
        "files": {
            "csv_files": [
                {
                    "filename": "users.csv",
                    "description": "用户基本信息数据",
                    "columns": ["id", "name", "email", "age", "department", "salary"],
                    "row_count": 5
                },
                {
                    "filename": "sales_data.csv", 
                    "description": "销售数据记录",
                    "columns": ["date", "product", "quantity", "revenue"],
                    "row_count": "动态生成"
                },
                {
                    "filename": "query_results.csv",
                    "description": "SQL 查询结果记录",
                    "columns": ["query_id", "question", "sql", "execution_time", "result_count"],
                    "row_count": 4
                }
            ],
            "image_files": [
                {
                    "filename": "sales_chart.svg",
                    "description": "产品销售柱状图",
                    "type": "chart",
                    "format": "SVG"
                },
                {
                    "filename": "department_distribution.svg",
                    "description": "部门人员分布饼图",
                    "type": "chart", 
                    "format": "SVG"
                },
                {
                    "filename": "sales_trend.svg",
                    "description": "月度销售趋势线图",
                    "type": "chart",
                    "format": "SVG"
                }
            ]
        }
    }
    
    metadata_file = os.path.join(data_dir, "metadata.json")
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    print(f"  ✅ 创建元数据: {metadata_file}")


def main():
    """主函数"""
    print("🚀 创建 Vanna Text-to-SQL 示例文件")
    print("="*50)
    
    # 创建 data 目录
    data_dir = create_data_directory()
    
    # 创建示例文件
    create_sample_csv_files(data_dir)
    create_sample_images(data_dir)
    create_metadata_file(data_dir)
    
    # 显示创建的文件
    print(f"\n📁 创建的文件列表:")
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            print(f"  📄 {file} ({file_size} bytes)")
    
    print(f"\n✅ 示例文件创建完成！")
    print(f"现在可以使用以下命令测试文件管理接口:")
    print(f"  python test_file_management.py")
    print(f"  python demo_file_integration.py")


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ 创建文件时出错: {str(e)}")
        print("请检查文件权限和磁盘空间")
