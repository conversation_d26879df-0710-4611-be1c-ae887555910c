# Vanna Text-to-SQL Server

基于 Vanna.ai 的文本转 SQL 查询服务器，支持自然语言查询数据库。

## 🚀 快速开始

### 1. 配置环境变量

```bash
# 复制配置模板
cp config/.env.example config/.env

# 编辑配置文件
nano config/.env
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 使用 Docker Compose 启动

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f vanna-app
```

### 4. 验证服务

```bash
# 健康检查
curl http://localhost:8001/health

# API 信息
curl http://localhost:8001/api/v1/info
```

## 📁 项目结构

```
project/
├── config/
│   ├── .env.example    # 配置模板
│   └── .env           # 实际配置 (需要创建)
├── app/               # 应用代码
├── logs/              # 日志文件
├── data/              # 数据文件
└── docker-compose.yml # Docker 配置
```

## 🔧 配置说明

详细配置说明请参考: [CONFIG_SETUP_GUIDE.md](CONFIG_SETUP_GUIDE.md)

## 📚 文档

- [健康检查指南](HEALTH_CHECK_GUIDE.md)
- [容器日志配置](CONTAINER_LOGGING_GUIDE.md)
- [API Key 认证指南](API_KEY_GUIDE.md)
- [Docker 部署指南](DOCKER_DEPLOYMENT_GUIDE.md)

## 🏗️ 构建镜像

```bash
# 多平台构建
docker buildx build --platform linux/amd64,linux/arm64 --compress -f Dockerfile -t registry.cn-hangzhou.aliyuncs.com/yifangyun-library/vanna_texttosql_server:v1.0.1 . --push

# 本地构建
docker build -t vanna-texttosql-server .
```
