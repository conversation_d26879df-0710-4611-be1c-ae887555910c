version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: vanna-mysql
    restart: unless-stopped
    ports:
      - "${VANNA_MYSQL_PORT:-3306}:3306"
    environment:
      VANNA_MYSQL_ROOT_PASSWORD: ${VANNA_MYSQL_ROOT_PASSWORD:-root_password}
      VANNA_MYSQL_DATABASE: ${VANNA_MYSQL_DATABASE:-vanna_db}
      VANNA_MYSQL_USER: ${VANNA_MYSQL_USER:-vanna_user}
      VANNA_MYSQL_PASSWORD: ${VANNA_MYSQL_PASSWORD:-vanna_password}
    volumes:
      - VANNA_MYSQL_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - vanna-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Vanna Text-to-SQL 应用服务
  vanna-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vanna-texttosql-server
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8001}:8001"
    environment:
      # Flask 配置
      FLASK_ENV: ${FLASK_ENV:-production}
      FLASK_DEBUG: ${FLASK_DEBUG:-False}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-this}

      # API Key 认证配置
      ENABLE_API_KEY_AUTH: ${ENABLE_API_KEY_AUTH:-true}
      API_KEYS: ${API_KEYS}
      
      # MySQL 配置
      VANNA_MYSQL_HOST: mysql
      VANNA_MYSQL_PORT: 3306
      VANNA_MYSQL_USER: ${VANNA_MYSQL_USER:-vanna_user}
      VANNA_MYSQL_PASSWORD: ${VANNA_MYSQL_PASSWORD:-vanna_password}
      VANNA_MYSQL_DATABASE: ${VANNA_MYSQL_DATABASE:-vanna_db}
      
      # OpenAI/DeepSeek 配置
      VANNA_OPEN_API_KEY: ${VANNA_OPEN_API_KEY}
      VANNA_OPEN_MODEL_NAME: ${VANNA_OPEN_MODEL_NAME:-deepseek-chat}
      VANNA_OPEN_BASE_URL: ${VANNA_OPEN_BASE_URL:-https://api.deepseek.com/v1}

      # 日志配置
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    networks:
      - vanna-network
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  VANNA_MYSQL_data:
    driver: local
  app_data:
    driver: local
  app_logs:
    driver: local

networks:
  vanna-network:
    driver: bridge
