version: '3.8'

services:
  

  # Vanna Text-to-SQL 应用服务
  vanna-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vanna-texttosql-server
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      # Flask 配置
      FLASK_ENV: ${FLASK_ENV:-production}
      FLASK_DEBUG: ${FLASK_DEBUG:-False}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-this}

      # API Key 认证配置
      ENABLE_API_KEY_AUTH: ${ENABLE_API_KEY_AUTH:-true}
      API_KEYS: ${API_KEYS}
      
      # MySQL 配置
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: ${MYSQL_USER:-vanna_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-vanna_password}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-vanna_db}
      
      # OpenAI/DeepSeek 配置
      OPEN_API_KEY: ${OPEN_API_KEY}
      OPEN_MODEL_NAME: ${OPEN_MODEL_NAME:-deepseek-chat}
      OPEN_BASE_URL: ${OPEN_BASE_URL:-https://api.deepseek.com/v1}
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    networks:
      - vanna-network
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  app_data:
    driver: local
  app_logs:
    driver: local

networks:
  vanna-network:
    driver: bridge
