#!/usr/bin/env python3
"""
测试健康检查接口
验证服务的健康状态
"""
import requests
import time
import sys


def test_health_endpoints():
    """测试所有健康检查端点"""
    print("🏥 测试健康检查接口")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8001"
    
    # 健康检查端点列表
    endpoints = [
        {
            "name": "主健康检查",
            "url": f"{base_url}/health",
            "expected_format": "json",
            "expected_content": {"status": "ok"}
        },
        {
            "name": "简单健康检查", 
            "url": f"{base_url}/health/simple",
            "expected_format": "text",
            "expected_content": "ok"
        },
        {
            "name": "API 健康检查",
            "url": f"{base_url}/api/v1/health", 
            "expected_format": "json",
            "expected_content": {"status": "ok"}
        }
    ]
    
    results = []
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n{i}. 测试: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            # 发送请求
            start_time = time.time()
            response = requests.get(endpoint['url'], timeout=5)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response_time:.2f} ms")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            # 检查响应内容
            if response.status_code == 200:
                if endpoint['expected_format'] == 'json':
                    try:
                        json_response = response.json()
                        print(f"   响应内容: {json_response}")
                        
                        # 验证期望的内容
                        expected = endpoint['expected_content']
                        if isinstance(expected, dict):
                            for key, value in expected.items():
                                if key in json_response and json_response[key] == value:
                                    print(f"   ✅ {key}: {value} (正确)")
                                else:
                                    print(f"   ❌ {key}: 期望 {value}, 实际 {json_response.get(key, 'N/A')}")
                        
                        results.append({
                            'endpoint': endpoint['name'],
                            'status': 'success',
                            'response_time': response_time,
                            'content': json_response
                        })
                        
                    except Exception as e:
                        print(f"   ❌ JSON 解析失败: {str(e)}")
                        print(f"   原始响应: {response.text}")
                        results.append({
                            'endpoint': endpoint['name'],
                            'status': 'json_error',
                            'error': str(e)
                        })
                        
                elif endpoint['expected_format'] == 'text':
                    text_response = response.text.strip()
                    print(f"   响应内容: '{text_response}'")
                    
                    if text_response == endpoint['expected_content']:
                        print(f"   ✅ 响应内容正确")
                        results.append({
                            'endpoint': endpoint['name'],
                            'status': 'success',
                            'response_time': response_time,
                            'content': text_response
                        })
                    else:
                        print(f"   ❌ 响应内容错误，期望: '{endpoint['expected_content']}', 实际: '{text_response}'")
                        results.append({
                            'endpoint': endpoint['name'],
                            'status': 'content_error',
                            'expected': endpoint['expected_content'],
                            'actual': text_response
                        })
                
            else:
                print(f"   ❌ HTTP 状态码错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                results.append({
                    'endpoint': endpoint['name'],
                    'status': 'http_error',
                    'status_code': response.status_code,
                    'content': response.text
                })
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未运行")
            results.append({
                'endpoint': endpoint['name'],
                'status': 'connection_error'
            })
            
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
            results.append({
                'endpoint': endpoint['name'],
                'status': 'timeout'
            })
            
        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")
            results.append({
                'endpoint': endpoint['name'],
                'status': 'error',
                'error': str(e)
            })
    
    # 显示测试总结
    print(f"\n" + "="*50)
    print("📊 测试总结")
    print("="*50)
    
    success_count = len([r for r in results if r['status'] == 'success'])
    total_count = len(results)
    
    print(f"总计测试: {total_count} 个端点")
    print(f"成功: {success_count} 个")
    print(f"失败: {total_count - success_count} 个")
    print(f"成功率: {(success_count/total_count)*100:.1f}%")
    
    # 显示详细结果
    print(f"\n📋 详细结果:")
    for result in results:
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"  {status_icon} {result['endpoint']}: {result['status']}")
        
        if 'response_time' in result:
            print(f"     响应时间: {result['response_time']:.2f} ms")
    
    # 性能分析
    successful_results = [r for r in results if 'response_time' in r]
    if successful_results:
        response_times = [r['response_time'] for r in successful_results]
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        
        print(f"\n⏱️  性能统计:")
        print(f"  平均响应时间: {avg_time:.2f} ms")
        print(f"  最快响应时间: {min_time:.2f} ms")
        print(f"  最慢响应时间: {max_time:.2f} ms")
    
    return success_count == total_count


def test_health_monitoring():
    """测试健康监控场景"""
    print(f"\n" + "="*50)
    print("🔍 健康监控场景测试")
    print("="*50)
    
    base_url = "http://localhost:8001"
    
    # 模拟监控系统的健康检查
    print(f"\n1. 模拟负载均衡器健康检查:")
    try:
        response = requests.get(f"{base_url}/health/simple", timeout=2)
        if response.status_code == 200 and response.text.strip() == 'ok':
            print("   ✅ 服务健康，可以接收流量")
        else:
            print("   ❌ 服务异常，应该移除流量")
    except:
        print("   ❌ 服务不可达，应该移除流量")
    
    # 模拟监控系统的详细检查
    print(f"\n2. 模拟监控系统详细检查:")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务状态: {data.get('status', 'unknown')}")
            print(f"   📝 消息: {data.get('message', 'N/A')}")
        else:
            print("   ❌ 服务状态检查失败")
    except:
        print("   ❌ 无法获取服务详细状态")
    
    # 模拟 Kubernetes 就绪探针
    print(f"\n3. 模拟 Kubernetes 就绪探针:")
    try:
        response = requests.get(f"{base_url}/health/simple", timeout=1)
        if response.status_code == 200:
            print("   ✅ Pod 就绪，可以接收请求")
        else:
            print("   ❌ Pod 未就绪")
    except:
        print("   ❌ Pod 健康检查失败")


def show_usage_examples():
    """显示使用示例"""
    print(f"\n" + "="*50)
    print("📚 健康检查使用示例")
    print("="*50)
    
    print(f"\n1. 基本健康检查:")
    print(f"""
# 简单检查（返回纯文本 "ok"）
curl http://localhost:8001/health/simple

# 详细检查（返回 JSON）
curl http://localhost:8001/health

# API 健康检查
curl http://localhost:8001/api/v1/health
""")
    
    print(f"\n2. 在脚本中使用:")
    print(f"""
#!/bin/bash
# 检查服务是否健康
if curl -f -s http://localhost:8001/health/simple > /dev/null; then
    echo "服务健康"
    exit 0
else
    echo "服务异常"
    exit 1
fi
""")
    
    print(f"\n3. Docker Compose 健康检查:")
    print(f"""
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8001/health/simple"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
""")
    
    print(f"\n4. Kubernetes 探针配置:")
    print(f"""
livenessProbe:
  httpGet:
    path: /health/simple
    port: 8001
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health/simple
    port: 8001
  initialDelaySeconds: 5
  periodSeconds: 5
""")


if __name__ == '__main__':
    try:
        # 运行健康检查测试
        success = test_health_endpoints()
        
        # 运行监控场景测试
        test_health_monitoring()
        
        # 显示使用示例
        show_usage_examples()
        
        if success:
            print(f"\n🎉 所有健康检查测试通过!")
            sys.exit(0)
        else:
            print(f"\n❌ 部分健康检查测试失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试脚本错误: {str(e)}")
        sys.exit(1)
