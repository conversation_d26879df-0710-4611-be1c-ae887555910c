#!/usr/bin/env python3
"""
测试删除所有训练数据功能
⚠️ 注意：这是危险操作，会删除所有训练数据！
"""
import requests
import json
import sys


def get_training_data_count(base_url, api_key):
    """获取当前训练数据数量"""
    headers = {"Authorization": f"Bearer {api_key}"}
    
    try:
        response = requests.get(f"{base_url}/api/v1/training-data", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data.get('data', {}).get('count', 0)
        else:
            print(f"❌ 获取训练数据失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取训练数据异常: {str(e)}")
        return None


def test_clear_all_training_data():
    """测试删除所有训练数据"""
    print("🧪 测试删除所有训练数据功能")
    print("="*50)
    
    # 配置
    base_url = "http://localhost:8001"
    api_key = "your-api-key-here"  # 请替换为实际的 API Key
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"⚠️ 警告：此操作将删除所有训练数据，不可恢复！")
    
    # 1. 获取当前训练数据数量
    print(f"\n1. 获取当前训练数据数量...")
    initial_count = get_training_data_count(base_url, api_key)
    
    if initial_count is None:
        print(f"❌ 无法获取训练数据数量，测试终止")
        return False
    
    print(f"   📊 当前训练数据数量: {initial_count}")
    
    if initial_count == 0:
        print(f"   ℹ️ 没有训练数据需要删除")
        
        # 测试删除空数据的情况
        print(f"\n2. 测试删除空数据...")
        response = requests.delete(
            f"{base_url}/api/v1/training-data/clear-all?confirm=yes",
            headers=headers,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 删除成功")
            print(f"   📋 删除结果: {data.get('data', {}).get('message', 'N/A')}")
            return True
        else:
            print(f"   ❌ 删除失败: {response.text[:200]}...")
            return False
    
    # 2. 测试不带确认参数的请求（应该失败）
    print(f"\n2. 测试不带确认参数的请求...")
    response = requests.delete(
        f"{base_url}/api/v1/training-data/clear-all",
        headers=headers,
        timeout=10
    )
    
    print(f"   状态码: {response.status_code}")
    
    if response.status_code == 400:
        print(f"   ✅ 正确拒绝了不带确认的请求")
        try:
            error_data = response.json()
            print(f"   📋 错误信息: {error_data.get('message', 'N/A')}")
        except:
            pass
    else:
        print(f"   ❌ 应该拒绝不带确认的请求")
        print(f"   响应: {response.text[:200]}...")
    
    # 3. 询问用户确认
    print(f"\n3. 确认删除操作...")
    print(f"   ⚠️ 即将删除 {initial_count} 条训练数据")
    print(f"   ⚠️ 此操作不可恢复！")
    
    while True:
        confirm = input(f"   🤔 确认删除所有训练数据? (yes/no): ").strip().lower()
        if confirm in ['yes', 'y']:
            break
        elif confirm in ['no', 'n']:
            print(f"   ⏹️ 用户取消操作")
            return True
        else:
            print(f"   ❓ 请输入 yes 或 no")
    
    # 4. 执行删除操作
    print(f"\n4. 执行删除操作...")
    response = requests.delete(
        f"{base_url}/api/v1/training-data/clear-all?confirm=yes",
        headers=headers,
        timeout=60  # 删除可能需要较长时间
    )
    
    print(f"   状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            result_data = data.get('data', {})
            
            print(f"   ✅ 删除操作完成")
            print(f"   📊 删除统计:")
            print(f"      总数量: {result_data.get('total_count', 0)}")
            print(f"      成功删除: {result_data.get('deleted_count', 0)}")
            print(f"      删除失败: {result_data.get('failed_count', 0)}")
            print(f"      消息: {result_data.get('message', 'N/A')}")
            
            # 显示删除的 ID（前几个）
            deleted_ids = result_data.get('deleted_ids', [])
            if deleted_ids:
                print(f"   📋 已删除的 ID (前5个): {deleted_ids[:5]}")
                if len(deleted_ids) > 5:
                    print(f"      ... 还有 {len(deleted_ids) - 5} 个")
            
            # 显示失败的 ID
            failed_ids = result_data.get('failed_ids', [])
            if failed_ids:
                print(f"   ❌ 删除失败的 ID: {failed_ids}")
            
            # 5. 验证删除结果
            print(f"\n5. 验证删除结果...")
            final_count = get_training_data_count(base_url, api_key)
            
            if final_count is not None:
                print(f"   📊 删除后训练数据数量: {final_count}")
                
                if final_count == 0:
                    print(f"   ✅ 所有训练数据已成功删除")
                    return True
                else:
                    print(f"   ⚠️ 仍有 {final_count} 条训练数据未删除")
                    return False
            else:
                print(f"   ❌ 无法验证删除结果")
                return False
            
        except json.JSONDecodeError:
            print(f"   ❌ JSON 解析失败")
            print(f"   响应: {response.text[:200]}...")
            return False
    else:
        print(f"   ❌ 删除失败: {response.status_code}")
        print(f"   响应: {response.text[:200]}...")
        return False


def show_api_usage():
    """显示 API 使用示例"""
    print(f"\n" + "="*50)
    print("📚 删除所有训练数据 API 使用示例")
    print("="*50)
    
    print(f"\n1. 基本用法:")
    print(f"""
# ⚠️ 危险操作：删除所有训练数据
curl -X DELETE "http://localhost:8001/api/v1/training-data/clear-all?confirm=yes" \\
  -H "Authorization: Bearer your-api-key"
""")
    
    print(f"\n2. 带 MySQL 参数:")
    print(f"""
curl -X DELETE "http://localhost:8001/api/v1/training-data/clear-all?confirm=yes&VANNA_MYSQL_host=localhost" \\
  -H "Authorization: Bearer your-api-key"
""")
    
    print(f"\n3. Python 示例:")
    print(f"""
import requests

base_url = "http://localhost:8001"
api_key = "your-api-key"
headers = {"Authorization": f"Bearer {{api_key}}"}

# ⚠️ 危险操作：删除所有训练数据
response = requests.delete(
    f"{{base_url}}/api/v1/training-data/clear-all?confirm=yes",
    headers=headers
)

if response.status_code == 200:
    result = response.json()
    print(f"删除完成: {{result['data']['message']}}")
else:
    print(f"删除失败: {{response.status_code}}")
""")
    
    print(f"\n4. 安全注意事项:")
    print(f"   - 必须提供 confirm=yes 参数")
    print(f"   - 操作不可恢复，请谨慎使用")
    print(f"   - 建议在删除前备份重要数据")
    print(f"   - 操作会被记录到日志中")


def main():
    """主测试函数"""
    print("🚀 删除所有训练数据测试")
    print("="*50)
    
    print(f"\n⚠️ 重要提醒:")
    print(f"   1. 此操作将删除所有训练数据")
    print(f"   2. 操作不可恢复")
    print(f"   3. 请确保已备份重要数据")
    print(f"   4. 请确保服务器正在运行")
    print(f"   5. 请在脚本中设置正确的 API Key")
    
    # 测试删除所有训练数据
    success = test_clear_all_training_data()
    
    # 显示使用示例
    show_api_usage()
    
    # 显示测试结果
    print(f"\n" + "="*50)
    print("📊 测试结果")
    print("="*50)
    
    if success:
        print(f"✅ 删除所有训练数据测试通过")
    else:
        print(f"❌ 删除所有训练数据测试失败")
        print(f"\n🔧 故障排除:")
        print(f"   1. 检查服务器状态: curl http://localhost:8001/health")
        print(f"   2. 检查 API Key 配置")
        print(f"   3. 查看服务器日志: docker-compose logs vanna-app")
    
    return success


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试脚本错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
