# 健康检查接口指南

## 概述

为 Vanna Text-to-SQL 服务器添加了多个健康检查接口，用于监控服务状态和实现负载均衡、容器编排等功能。

## 可用的健康检查端点

### 1. 简单健康检查
- **路径**: `GET /health/simple`
- **响应**: 纯文本 `ok`
- **用途**: 负载均衡器、Kubernetes 探针等需要简单响应的场景

```bash
curl http://localhost:8001/health/simple
# 响应: ok
```

### 2. 详细健康检查
- **路径**: `GET /health`
- **响应**: JSON 格式
- **用途**: 监控系统、详细状态检查

```bash
curl http://localhost:8001/health
# 响应: {"status": "ok", "message": "Service is running"}
```

### 3. API 健康检查
- **路径**: `GET /api/v1/health`
- **响应**: JSON 格式
- **用途**: API 层面的健康检查

```bash
curl http://localhost:8001/api/v1/health
# 响应: {"status": "ok"}
```

## 使用场景

### 1. 负载均衡器配置

#### Nginx
```nginx
upstream vanna_backend {
    server 127.0.0.1:8001;
    # 其他服务器...
}

server {
    location /health/simple {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    location / {
        proxy_pass http://vanna_backend;
        # 健康检查
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
    }
}
```

#### HAProxy
```
backend vanna_servers
    balance roundrobin
    option httpchk GET /health/simple
    http-check expect status 200
    server vanna1 127.0.0.1:8001 check
```

### 2. Docker Compose 健康检查

```yaml
services:
  vanna-app:
    # ... 其他配置
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### 3. Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vanna-texttosql
spec:
  template:
    spec:
      containers:
      - name: vanna-app
        image: vanna-texttosql-server:latest
        ports:
        - containerPort: 8001
        livenessProbe:
          httpGet:
            path: /health/simple
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/simple
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
```

### 4. 监控脚本

#### Bash 脚本
```bash
#!/bin/bash
# 健康检查脚本

SERVICE_URL="http://localhost:8001/health/simple"
MAX_RETRIES=3
RETRY_DELAY=5

for i in $(seq 1 $MAX_RETRIES); do
    if curl -f -s "$SERVICE_URL" > /dev/null; then
        echo "✅ 服务健康 (尝试 $i/$MAX_RETRIES)"
        exit 0
    else
        echo "❌ 服务异常 (尝试 $i/$MAX_RETRIES)"
        if [ $i -lt $MAX_RETRIES ]; then
            sleep $RETRY_DELAY
        fi
    fi
done

echo "🚨 服务持续异常，需要人工介入"
exit 1
```

#### Python 监控脚本
```python
import requests
import time
import logging

def check_service_health(url, timeout=5):
    """检查服务健康状态"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200 and response.text.strip() == 'ok'
    except:
        return False

def monitor_service():
    """持续监控服务"""
    url = "http://localhost:8001/health/simple"
    
    while True:
        if check_service_health(url):
            logging.info("✅ 服务健康")
        else:
            logging.error("❌ 服务异常")
            # 发送告警通知
            send_alert("Vanna 服务异常")
        
        time.sleep(60)  # 每分钟检查一次

if __name__ == "__main__":
    monitor_service()
```

## 响应格式

### 简单健康检查响应
```
HTTP/1.1 200 OK
Content-Type: text/plain; charset=utf-8
Content-Length: 2

ok
```

### 详细健康检查响应
```
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 52

{
  "status": "ok",
  "message": "Service is running"
}
```

## 测试工具

### 使用测试脚本
```bash
python test_health_check.py
```

### 手动测试
```bash
# 测试所有健康检查端点
curl http://localhost:8001/health/simple
curl http://localhost:8001/health
curl http://localhost:8001/api/v1/health

# 测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8001/health/simple
```

### curl 格式文件 (curl-format.txt)
```
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
```

## 性能考虑

### 1. 响应时间
- 健康检查应该在 100ms 内响应
- 避免在健康检查中执行复杂操作
- 不要在健康检查中连接外部服务

### 2. 资源使用
- 健康检查不应消耗大量 CPU 或内存
- 避免在健康检查中记录详细日志
- 考虑健康检查的频率和超时设置

### 3. 缓存策略
- 可以考虑缓存健康状态（如果检查成本较高）
- 设置合理的缓存过期时间

## 故障排除

### 常见问题

1. **健康检查超时**
   - 检查服务是否正常运行
   - 确认端口配置正确
   - 检查网络连接

2. **返回 404 错误**
   - 确认健康检查路径正确
   - 检查路由配置

3. **返回 500 错误**
   - 查看应用日志
   - 检查应用是否正常启动

### 调试命令
```bash
# 检查服务是否监听正确端口
netstat -tlnp | grep 8001

# 检查容器健康状态
docker ps
docker inspect <container_id> | grep Health

# 查看健康检查日志
docker logs <container_id> | grep health
```

## 最佳实践

1. **多层健康检查**
   - 使用简单检查用于快速响应
   - 使用详细检查用于深度监控

2. **合理的超时设置**
   - 健康检查超时应该短于服务间调用超时
   - 考虑网络延迟和服务启动时间

3. **渐进式健康检查**
   - 启动时使用较长的初始延迟
   - 运行时使用较短的检查间隔

4. **监控和告警**
   - 监控健康检查的成功率
   - 设置合理的告警阈值
   - 记录健康检查的历史数据

## 总结

健康检查接口提供了：
- 🏥 **多种检查方式** - 简单文本和详细 JSON 响应
- 🔄 **容器编排支持** - Docker 和 Kubernetes 集成
- 📊 **监控友好** - 适合各种监控系统
- ⚡ **高性能** - 快速响应，低资源消耗

通过合理配置和使用这些健康检查接口，可以确保服务的高可用性和稳定性。
