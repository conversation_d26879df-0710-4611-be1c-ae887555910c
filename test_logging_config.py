#!/usr/bin/env python3
"""
测试日志配置
验证日志是否正确记录到 /app/logs 目录
"""
import requests
import time
import os
import json


def test_logging_in_container():
    """测试容器中的日志记录"""
    print("📝 测试容器日志配置")
    print("="*50)
    
    base_url = "http://localhost:8001"
    
    # 测试用例 - 触发不同类型的日志
    test_cases = [
        {
            "name": "健康检查 (INFO 日志)",
            "method": "GET",
            "url": f"{base_url}/health",
            "expected_log_level": "INFO"
        },
        {
            "name": "API 信息查询 (INFO 日志)",
            "method": "GET", 
            "url": f"{base_url}/api/v1/info",
            "expected_log_level": "INFO"
        },
        {
            "name": "无效端点 (ERROR 日志)",
            "method": "GET",
            "url": f"{base_url}/api/v1/nonexistent",
            "expected_log_level": "ERROR"
        },
        {
            "name": "无效 API Key (WARNING 日志)",
            "method": "POST",
            "url": f"{base_url}/api/v1/train",
            "headers": {"Authorization": "Bearer invalid-key"},
            "data": {"ddl": "CREATE TABLE test (id INT);"},
            "expected_log_level": "WARNING"
        }
    ]
    
    print(f"\n🧪 执行测试用例...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        
        try:
            # 发送请求
            kwargs = {
                'timeout': 5,
                'headers': test_case.get('headers', {})
            }
            
            if test_case.get('data'):
                kwargs['json'] = test_case['data']
                kwargs['headers']['Content-Type'] = 'application/json'
            
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], **kwargs)
            elif test_case['method'] == 'POST':
                response = requests.post(test_case['url'], **kwargs)
            
            print(f"   状态码: {response.status_code}")
            print(f"   预期日志级别: {test_case['expected_log_level']}")
            
        except Exception as e:
            print(f"   请求异常: {str(e)}")
        
        # 等待日志写入
        time.sleep(0.5)
    
    print(f"\n✅ 测试用例执行完成")
    print(f"\n💡 请检查以下位置的日志文件:")
    print(f"   - 容器内: /app/logs/app.log")
    print(f"   - 容器内: /app/logs/error.log") 
    print(f"   - 主机上: ./logs/app.log (如果挂载了卷)")
    print(f"   - 主机上: ./logs/error.log (如果挂载了卷)")


def check_log_files():
    """检查日志文件是否存在"""
    print(f"\n📁 检查日志文件...")
    
    log_paths = [
        "./logs/app.log",
        "./logs/error.log"
    ]
    
    for log_path in log_paths:
        if os.path.exists(log_path):
            file_size = os.path.getsize(log_path)
            print(f"   ✅ {log_path} 存在 ({file_size} bytes)")
            
            # 显示最后几行日志
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"      最后 3 行:")
                        for line in lines[-3:]:
                            print(f"        {line.strip()}")
                    else:
                        print(f"      文件为空")
            except Exception as e:
                print(f"      读取失败: {str(e)}")
        else:
            print(f"   ❌ {log_path} 不存在")


def show_docker_log_commands():
    """显示 Docker 日志查看命令"""
    print(f"\n" + "="*50)
    print("🐳 Docker 日志查看命令")
    print("="*50)
    
    print(f"\n1. 查看容器日志 (stdout/stderr):")
    print(f"   docker-compose logs -f vanna-app")
    print(f"   docker logs -f <container_id>")
    
    print(f"\n2. 进入容器查看日志文件:")
    print(f"   docker exec -it <container_id> bash")
    print(f"   ls -la /app/logs/")
    print(f"   tail -f /app/logs/app.log")
    print(f"   tail -f /app/logs/error.log")
    
    print(f"\n3. 从容器复制日志文件:")
    print(f"   docker cp <container_id>:/app/logs/app.log ./app.log")
    print(f"   docker cp <container_id>:/app/logs/error.log ./error.log")
    
    print(f"\n4. 实时监控日志:")
    print(f"   # 监控应用日志")
    print(f"   docker exec -it <container_id> tail -f /app/logs/app.log")
    print(f"   ")
    print(f"   # 监控错误日志")
    print(f"   docker exec -it <container_id> tail -f /app/logs/error.log")
    
    print(f"\n5. 搜索特定日志:")
    print(f"   # 搜索错误日志")
    print(f"   docker exec -it <container_id> grep ERROR /app/logs/app.log")
    print(f"   ")
    print(f"   # 搜索今天的日志")
    print(f"   docker exec -it <container_id> grep $(date +%Y-%m-%d) /app/logs/app.log")


def show_log_configuration():
    """显示日志配置说明"""
    print(f"\n" + "="*50)
    print("⚙️  日志配置说明")
    print("="*50)
    
    print(f"\n📋 日志文件:")
    print(f"   - /app/logs/app.log    : 所有级别的日志 (INFO, WARNING, ERROR)")
    print(f"   - /app/logs/error.log  : 只有错误日志 (ERROR)")
    
    print(f"\n📊 日志轮转:")
    print(f"   - 最大文件大小: 10MB")
    print(f"   - 保留文件数: 5 个")
    print(f"   - 轮转后文件: app.log.1, app.log.2, ...")
    
    print(f"\n🎛️  日志级别 (通过 LOG_LEVEL 环境变量控制):")
    print(f"   - DEBUG   : 调试信息 (开发环境)")
    print(f"   - INFO    : 一般信息 (默认)")
    print(f"   - WARNING : 警告信息")
    print(f"   - ERROR   : 错误信息")
    print(f"   - CRITICAL: 严重错误")
    
    print(f"\n📝 日志格式:")
    print(f"   时间戳 - 模块名 - 级别 - 消息")
    print(f"   例如: 2023-12-01 10:30:45,123 - app.api.routes - INFO - 收到训练请求")
    
    print(f"\n🔧 环境变量配置:")
    print(f"   LOG_LEVEL=INFO     # 设置日志级别")
    print(f"   LOG_DIR=/app/logs  # 设置日志目录")


def test_log_levels():
    """测试不同日志级别"""
    print(f"\n🎯 测试不同日志级别...")
    
    # 这个函数需要在容器内运行才能真正测试
    print(f"   💡 要测试不同日志级别，请:")
    print(f"   1. 修改 docker-compose.yml 中的 LOG_LEVEL 环境变量")
    print(f"   2. 重启容器: docker-compose restart vanna-app")
    print(f"   3. 发送一些请求")
    print(f"   4. 检查日志文件内容")
    
    print(f"\n   示例:")
    print(f"   # 设置为 DEBUG 级别")
    print(f"   LOG_LEVEL=DEBUG docker-compose up -d vanna-app")
    print(f"   ")
    print(f"   # 设置为 ERROR 级别")
    print(f"   LOG_LEVEL=ERROR docker-compose up -d vanna-app")


if __name__ == '__main__':
    try:
        print("🚀 开始测试日志配置")
        
        # 检查本地日志文件
        check_log_files()
        
        # 测试日志记录
        test_logging_in_container()
        
        # 显示 Docker 命令
        show_docker_log_commands()
        
        # 显示配置说明
        show_log_configuration()
        
        # 测试日志级别
        test_log_levels()
        
        print(f"\n🎉 日志测试完成!")
        print(f"\n📋 下一步:")
        print(f"   1. 启动容器: docker-compose up -d")
        print(f"   2. 发送一些请求触发日志")
        print(f"   3. 检查 /app/logs/ 目录中的日志文件")
        print(f"   4. 使用上面的 Docker 命令查看日志")
        
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试错误: {str(e)}")
        import traceback
        traceback.print_exc()
