#!/usr/bin/env python3
"""
API Key 认证使用演示
展示如何正确使用 API Key 调用各种端点
"""
import requests
import json


def demo_api_usage():
    """演示 API 使用方法"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🚀 Vanna Text-to-SQL API Key 认证演示")
    print("="*50)
    
    # 1. 首先生成一个测试用的 API Key
    print("\n1️⃣ 生成测试 API Key...")
    from app.auth import APIKeyManager
    test_api_key = APIKeyManager.generate_api_key(32)
    print(f"生成的测试 API Key: {test_api_key}")
    print("⚠️  注意: 在实际使用中，请将此 API Key 添加到 .env 文件的 API_KEYS 中")
    
    # 2. 测试认证信息端点（无需认证）
    print("\n2️⃣ 测试认证信息端点...")
    try:
        response = requests.get(f"{base_url}/auth/info")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 3. 测试 API Key 验证端点
    print("\n3️⃣ 测试 API Key 验证...")
    try:
        response = requests.post(
            f"{base_url}/auth/validate-key",
            json={"api_key": test_api_key}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 4. 演示不同的认证头格式
    print("\n4️⃣ 演示不同的认证头格式...")
    
    auth_methods = [
        ("Authorization Bearer", {"Authorization": f"Bearer {test_api_key}"}),
        ("X-API-Key", {"X-API-Key": test_api_key}),
        ("API-Key", {"API-Key": test_api_key})
    ]
    
    for method_name, headers in auth_methods:
        print(f"\n  📋 使用 {method_name} 头:")
        try:
            response = requests.get(f"{base_url}/auth/info", headers=headers)
            result = response.json()
            authenticated = result.get('data', {}).get('authenticated', False)
            print(f"    状态码: {response.status_code}")
            print(f"    认证状态: {'✅ 成功' if authenticated else '❌ 失败'}")
        except Exception as e:
            print(f"    错误: {e}")
    
    # 5. 演示受保护端点的访问
    print("\n5️⃣ 演示受保护端点访问...")
    
    headers = {"Authorization": f"Bearer {test_api_key}"}
    
    # 测试训练端点（只测试认证，不提供完整数据）
    print("\n  📚 测试训练端点认证:")
    try:
        response = requests.post(
            f"{base_url}/train",
            headers=headers,
            json={}  # 空数据，期望参数错误但认证通过
        )
        print(f"    状态码: {response.status_code}")
        if response.status_code == 400:
            print("    ✅ 认证通过（参数错误是预期的）")
        elif response.status_code == 401:
            print("    ❌ 认证失败")
        else:
            print(f"    响应: {response.json()}")
    except Exception as e:
        print(f"    错误: {e}")
    
    # 测试查询端点
    print("\n  🔍 测试查询端点认证:")
    try:
        response = requests.post(
            f"{base_url}/ask",
            headers=headers,
            json={}  # 空数据，期望参数错误但认证通过
        )
        print(f"    状态码: {response.status_code}")
        if response.status_code == 400:
            print("    ✅ 认证通过（参数错误是预期的）")
        elif response.status_code == 401:
            print("    ❌ 认证失败")
        else:
            print(f"    响应: {response.json()}")
    except Exception as e:
        print(f"    错误: {e}")
    
    # 6. 演示无认证访问受保护端点
    print("\n6️⃣ 演示无认证访问受保护端点...")
    try:
        response = requests.post(f"{base_url}/train", json={})
        print(f"状态码: {response.status_code}")
        if response.status_code == 401:
            print("✅ 正确拒绝了无认证访问")
            print(f"错误信息: {response.json().get('message', 'N/A')}")
        else:
            print("❌ 应该拒绝无认证访问")
    except Exception as e:
        print(f"错误: {e}")
    
    # 7. 完整的使用示例
    print("\n7️⃣ 完整使用示例代码:")
    print("""
# Python 代码示例
import requests

# 配置
api_key = "your-api-key-here"
base_url = "http://localhost:8000/api/v1"
headers = {"Authorization": f"Bearer {api_key}"}

# 训练模型
train_data = {
    "ddl": "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100));",
    "documentation": "用户表包含用户基本信息"
}
response = requests.post(f"{base_url}/train", headers=headers, json=train_data)

# 查询
ask_data = {"question": "查询所有用户"}
response = requests.post(f"{base_url}/ask", headers=headers, json=ask_data)
""")
    
    print("\n" + "="*50)
    print("📝 重要提示:")
    print("1. 将生成的 API Key 添加到 .env 文件中")
    print("2. 确保服务器正在运行 (python run.py)")
    print("3. 在生产环境中使用强密码和 HTTPS")
    print("4. 定期轮换 API Keys")


if __name__ == '__main__':
    try:
        demo_api_usage()
    except Exception as e:
        print(f"\n❌ 演示错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行")
        print("2. 已安装所需依赖")
        print("3. 网络连接正常")
