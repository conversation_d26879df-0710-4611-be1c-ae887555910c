#!/usr/bin/env python3
"""
快速测试训练数据获取接口
"""
import requests
import json


def quick_test():
    """快速测试"""
    print("🚀 快速测试训练数据获取接口")
    
    # 配置 - 请根据实际情况修改
    base_url = "http://localhost:8001"
    api_key = "your-api-key-here"  # 请替换为实际的 API Key
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📋 发送请求...")
        response = requests.get(
            f"{base_url}/api/v1/training-data",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 训练数据数量: {data.get('data', {}).get('count', 0)}")
            return True
        else:
            print(f"❌ 请求失败")
            print(f"响应: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


if __name__ == '__main__':
    success = quick_test()
    if success:
        print(f"\n🎉 测试通过! DataFrame 错误已修复。")
    else:
        print(f"\n⚠️  测试失败，请检查:")
        print(f"   1. 服务器是否运行: curl http://localhost:8001/health")
        print(f"   2. API Key 是否正确")
        print(f"   3. 查看服务器日志: docker-compose logs vanna-app")
