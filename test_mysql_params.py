#!/usr/bin/env python3
"""
测试 train 和 ask 接口的 MySQL 参数功能
"""
import requests
import json
import time


def test_mysql_params():
    """测试 MySQL 参数功能"""
    base_url = "http://localhost:8000/api/v1"
    
    # 请替换为你的实际 API Key
    api_key = "your-api-key-here"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🚀 测试 MySQL 参数功能")
    print("="*60)
    
    # 测试数据
    mysql_configs = [
        {
            "name": "默认配置",
            "config": {}  # 使用默认配置
        },
        {
            "name": "自定义配置 1",
            "config": {
                "mysql_host": "localhost",
                "mysql_port": 3306,
                "mysql_user": "test_user",
                "mysql_password": "test_password",
                "mysql_database": "test_db"
            }
        },
        {
            "name": "自定义配置 2 (部分参数)",
            "config": {
                "mysql_host": "*************",
                "mysql_database": "another_db"
                # 其他参数使用默认值
            }
        }
    ]
    
    # 1. 测试 train 接口
    print("\n1️⃣ 测试 train 接口的 MySQL 参数...")
    
    for i, test_case in enumerate(mysql_configs):
        print(f"\n  📋 测试 {test_case['name']}:")
        
        train_data = {
            "ddl": "CREATE TABLE test_users (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(100));",
            "documentation": "测试用户表",
            **test_case['config']  # 合并 MySQL 配置
        }
        
        try:
            response = requests.post(
                f"{base_url}/train",
                headers=headers,
                json=train_data,
                timeout=10
            )
            
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("    ✅ 请求成功")
                if 'data' in result and 'mysql_config' in result['data']:
                    mysql_info = result['data']['mysql_config']
                    print(f"    使用的 MySQL 配置:")
                    print(f"      - 主机: {mysql_info.get('host', 'N/A')}")
                    print(f"      - 端口: {mysql_info.get('port', 'N/A')}")
                    print(f"      - 用户: {mysql_info.get('user', 'N/A')}")
                    print(f"      - 数据库: {mysql_info.get('database', 'N/A')}")
            else:
                print("    ❌ 请求失败")
                try:
                    error_info = response.json()
                    print(f"    错误: {error_info.get('message', 'Unknown error')}")
                except:
                    print(f"    错误: {response.text}")
                    
        except Exception as e:
            print(f"    ❌ 异常: {str(e)}")
    
    # 2. 测试 ask 接口
    print(f"\n2️⃣ 测试 ask 接口的 MySQL 参数...")
    
    for i, test_case in enumerate(mysql_configs):
        print(f"\n  📋 测试 {test_case['name']}:")
        
        ask_data = {
            "question": "查询所有用户信息",
            **test_case['config']  # 合并 MySQL 配置
        }
        
        try:
            response = requests.post(
                f"{base_url}/ask",
                headers=headers,
                json=ask_data,
                timeout=10
            )
            
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("    ✅ 请求成功")
                if 'data' in result and 'mysql_config' in result['data']:
                    mysql_info = result['data']['mysql_config']
                    print(f"    使用的 MySQL 配置:")
                    print(f"      - 主机: {mysql_info.get('host', 'N/A')}")
                    print(f"      - 端口: {mysql_info.get('port', 'N/A')}")
                    print(f"      - 用户: {mysql_info.get('user', 'N/A')}")
                    print(f"      - 数据库: {mysql_info.get('database', 'N/A')}")
            else:
                print("    ❌ 请求失败")
                try:
                    error_info = response.json()
                    print(f"    错误: {error_info.get('message', 'Unknown error')}")
                except:
                    print(f"    错误: {response.text}")
                    
        except Exception as e:
            print(f"    ❌ 异常: {str(e)}")
    
    # 3. 测试错误情况
    print(f"\n3️⃣ 测试错误情况...")
    
    error_cases = [
        {
            "name": "缺少必需参数",
            "data": {
                "question": "测试问题",
                "mysql_host": "",  # 空主机
                "mysql_user": "",  # 空用户
                "mysql_database": ""  # 空数据库
            }
        },
        {
            "name": "无效端口",
            "data": {
                "question": "测试问题",
                "mysql_host": "localhost",
                "mysql_port": "invalid_port",
                "mysql_user": "test",
                "mysql_database": "test"
            }
        }
    ]
    
    for test_case in error_cases:
        print(f"\n  📋 测试 {test_case['name']}:")
        
        try:
            response = requests.post(
                f"{base_url}/ask",
                headers=headers,
                json=test_case['data'],
                timeout=10
            )
            
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 400:
                print("    ✅ 正确返回错误状态")
                try:
                    error_info = response.json()
                    print(f"    错误信息: {error_info.get('message', 'N/A')}")
                except:
                    pass
            else:
                print("    ❌ 应该返回 400 错误")
                
        except Exception as e:
            print(f"    ❌ 异常: {str(e)}")
    
    print(f"\n" + "="*60)
    print("📝 使用说明:")
    print("1. 请将 'your-api-key-here' 替换为实际的 API Key")
    print("2. 确保服务器正在运行")
    print("3. MySQL 参数是可选的，不提供时使用默认配置")
    print("4. 支持的 MySQL 参数:")
    print("   - mysql_host: MySQL 主机地址")
    print("   - mysql_port: MySQL 端口")
    print("   - mysql_user: MySQL 用户名")
    print("   - mysql_password: MySQL 密码")
    print("   - mysql_database: MySQL 数据库名")


def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("📚 使用示例")
    print("="*60)
    
    print("\n1. 使用默认 MySQL 配置的训练请求:")
    print("""
curl -X POST http://localhost:8000/api/v1/train \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100));",
    "documentation": "用户表"
  }'
""")
    
    print("\n2. 使用自定义 MySQL 配置的训练请求:")
    print("""
curl -X POST http://localhost:8000/api/v1/train \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100));",
    "documentation": "用户表",
    "mysql_host": "*************",
    "mysql_port": 3306,
    "mysql_user": "custom_user",
    "mysql_password": "custom_password",
    "mysql_database": "custom_db"
  }'
""")
    
    print("\n3. 使用自定义 MySQL 配置的查询请求:")
    print("""
curl -X POST http://localhost:8000/api/v1/ask \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "question": "查询所有用户",
    "mysql_host": "localhost",
    "mysql_database": "production_db"
  }'
""")


if __name__ == '__main__':
    try:
        test_mysql_params()
        show_usage_examples()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行在 http://localhost:8000")
        print("2. 已配置有效的 API Key")
        print("3. 网络连接正常")
