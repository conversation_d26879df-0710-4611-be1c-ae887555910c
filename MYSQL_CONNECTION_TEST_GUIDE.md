# MySQL 连接测试接口指南

## 概述

新增的 MySQL 连接测试接口 (`/api/v1/mysql/test-connection`) 允许您在执行实际的训练或查询操作之前，验证 MySQL 连接参数是否正确。这个接口只测试连接，不会执行任何数据操作。

## 接口信息

- **路径**: `POST /api/v1/mysql/test-connection`
- **认证**: 需要 API Key
- **功能**: 测试 MySQL 连接并返回详细信息

## 支持的参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `VANNA_MYSQL_host` | string | 可选 | MySQL 主机地址 |
| `VANNA_MYSQL_port` | integer | 可选 | MySQL 端口号 (默认 3306) |
| `VANNA_MYSQL_user` | string | 可选 | MySQL 用户名 |
| `VANNA_MYSQL_password` | string | 可选 | MySQL 密码 |
| `VANNA_MYSQL_database` | string | 可选 | MySQL 数据库名 |

## 使用示例

### 1. 测试默认配置

```bash
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 测试自定义配置

```bash
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "VANNA_MYSQL_host": "localhost",
    "VANNA_MYSQL_port": 3306,
    "VANNA_MYSQL_user": "test_user",
    "VANNA_MYSQL_password": "test_password",
    "VANNA_MYSQL_database": "test_db"
  }'
```

### 3. 只测试服务器连接

```bash
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "VANNA_MYSQL_host": "production-db.company.com",
    "VANNA_MYSQL_user": "readonly_user"
  }'
```

## 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "MySQL 连接测试成功",
  "data": {
    "connection_status": "success",
    "mysql_config": {
      "host": "localhost",
      "port": 3306,
      "user": "root",
      "database": "test"
    },
    "server_info": {
      "version": "8.0.33",
      "host": "localhost",
      "port": 3306
    },
    "database_exists": true,
    "response_time_ms": 15.23
  }
}
```

### 失败响应

```json
{
  "success": false,
  "message": "MySQL 连接失败: 用户名或密码错误",
  "error_code": "VANNA_MYSQL_CONNECTION_FAILED",
  "data": null
}
```

## 返回字段说明

| 字段 | 说明 |
|------|------|
| `connection_status` | 连接状态 ("success" 或 "failed") |
| `mysql_config` | 使用的 MySQL 配置 (不包含密码) |
| `server_info.version` | MySQL 服务器版本 |
| `database_exists` | 指定的数据库是否存在 |
| `response_time_ms` | 连接响应时间 (毫秒) |

## 错误处理

接口会根据不同的错误情况返回友好的错误信息：

| MySQL 错误代码 | 友好错误信息 |
|----------------|--------------|
| 1045 | 用户名或密码错误 |
| 1049 | 数据库不存在 |
| 2003 | 无法连接到 MySQL 服务器 |
| 1044 | 用户没有访问数据库的权限 |

## Python 示例

```python
import requests

def test_VANNA_MYSQL_connection(api_key, mysql_config=None):
    """测试 MySQL 连接"""
    url = "http://localhost:8000/api/v1/mysql/test-connection"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    data = mysql_config or {}
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=10)
        result = response.json()
        
        if response.status_code == 200:
            data = result['data']
            print("✅ 连接成功!")
            print(f"服务器版本: {data['server_info']['version']}")
            print(f"响应时间: {data['response_time_ms']}ms")
            print(f"数据库存在: {'是' if data['database_exists'] else '否'}")
            return True
        else:
            print(f"❌ 连接失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

# 使用示例
api_key = "your-api-key-here"

# 测试默认配置
test_VANNA_MYSQL_connection(api_key)

# 测试自定义配置
custom_config = {
    "VANNA_MYSQL_host": "localhost",
    "VANNA_MYSQL_user": "test_user",
    "VANNA_MYSQL_password": "test_password",
    "VANNA_MYSQL_database": "test_db"
}
test_VANNA_MYSQL_connection(api_key, custom_config)
```

## 使用场景

### 1. 部署前验证
在部署应用到新环境前，验证数据库连接配置是否正确。

### 2. 故障排除
当训练或查询接口出现连接问题时，使用此接口快速定位问题。

### 3. 配置测试
测试不同的数据库配置，确保参数正确。

### 4. 健康检查
定期检查数据库连接状态，监控系统健康。

## 最佳实践

1. **先测试连接** - 在使用 `train` 和 `ask` 接口前，先用此接口验证连接
2. **监控响应时间** - 关注 `response_time_ms` 字段，评估网络延迟
3. **检查数据库存在性** - 确认 `database_exists` 字段，避免后续操作失败
4. **处理错误信息** - 根据返回的错误信息进行相应的故障排除

## 安全注意事项

1. **密码保护** - 响应中不会返回密码信息
2. **超时设置** - 连接超时设置为 10 秒，避免长时间等待
3. **错误信息** - 提供详细但安全的错误信息，不泄露敏感信息

## 测试工具

提供了以下测试脚本：

- `test_VANNA_MYSQL_connection.py` - 自动化测试脚本
- `demo_VANNA_MYSQL_connection.py` - 交互式演示脚本

运行测试：
```bash
python test_VANNA_MYSQL_connection.py
python demo_VANNA_MYSQL_connection.py
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查主机地址和端口
   - 确认网络连接
   - 检查防火墙设置

2. **认证失败**
   - 验证用户名和密码
   - 确认用户权限

3. **数据库不存在**
   - 检查数据库名称
   - 确认数据库已创建

### 调试步骤

1. 先测试不带数据库名的连接
2. 确认服务器连接正常后，再测试具体数据库
3. 检查用户权限和数据库访问权限
4. 查看服务器日志获取详细错误信息
