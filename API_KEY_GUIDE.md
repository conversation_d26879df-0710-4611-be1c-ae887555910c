# API Key 认证指南

## 概述

Vanna Text-to-SQL 服务器现在支持 API Key 认证，确保只有授权用户才能访问敏感的 API 端点。

## 功能特性

- ✅ **多 API Key 支持** - 可配置多个有效的 API Key
- ✅ **多种认证方式** - 支持多种 HTTP 头格式
- ✅ **安全验证** - 使用时序安全的字符串比较
- ✅ **详细日志** - 记录所有认证尝试
- ✅ **灵活配置** - 可启用/禁用认证功能
- ✅ **管理端点** - 提供 API Key 管理功能

## 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 启用 API Key 认证
ENABLE_API_KEY_AUTH=true

# 配置多个 API Key（用逗号分隔）
API_KEYS=your-api-key-1,your-api-key-2,your-api-key-3
```

### 2. 生成 API Keys

使用提供的工具生成安全的 API Keys：

```bash
# 生成单个 API Key
python generate_api_keys.py generate

# 生成多个 API Key
python generate_api_keys.py generate -c 5 -l 32

# 批量生成不同级别的 API Keys
python generate_api_keys.py batch
```

## 使用方法

### 支持的认证头格式

API Key 可以通过以下三种 HTTP 头传递：

1. **Authorization Bearer** (推荐)
```bash
Authorization: Bearer your-api-key-here
```

2. **X-API-Key**
```bash
X-API-Key: your-api-key-here
```

3. **API-Key**
```bash
API-Key: your-api-key-here
```

### 请求示例

#### 使用 curl

```bash
# 训练模型 (使用默认 MySQL 配置)
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100));",
    "documentation": "用户表"
  }'

# 训练模型 (使用自定义 MySQL 配置)
curl -X POST http://localhost:8000/api/v1/train \
  -H "Authorization: Bearer your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE users (id INT, name VARCHAR(100));",
    "documentation": "用户表",
    "mysql_host": "*************",
    "mysql_port": 3306,
    "mysql_user": "custom_user",
    "mysql_password": "custom_password",
    "mysql_database": "custom_db"
  }'

# 查询 (使用默认 MySQL 配置)
curl -X POST http://localhost:8000/api/v1/ask \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户"
  }'

# 查询 (使用自定义 MySQL 配置)
curl -X POST http://localhost:8000/api/v1/ask \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "查询所有用户",
    "mysql_host": "localhost",
    "mysql_database": "production_db"
  }'
```

#### 使用 Python requests

```python
import requests

headers = {
    'Authorization': 'Bearer your-api-key-here',
    'Content-Type': 'application/json'
}

# 训练请求
response = requests.post(
    'http://localhost:8000/api/v1/train',
    headers=headers,
    json={
        'ddl': 'CREATE TABLE users (id INT, name VARCHAR(100));',
        'documentation': '用户表'
    }
)

# 查询请求
response = requests.post(
    'http://localhost:8000/api/v1/ask',
    headers=headers,
    json={'question': '查询所有用户'}
)
```

## MySQL 连接参数

### 支持的 MySQL 参数

`train`、`ask` 和 `mysql/test-connection` 接口支持动态传入 MySQL 连接参数，**所有参数都是可选的**：

- `mysql_host` - MySQL 主机地址 (可选)
- `mysql_port` - MySQL 端口号 (可选，默认 3306)
- `mysql_user` - MySQL 用户名 (可选)
- `mysql_password` - MySQL 密码 (可选)
- `mysql_database` - MySQL 数据库名 (可选)

### 参数优先级和默认行为

1. **请求参数优先** - 如果在请求中提供了 MySQL 参数，将使用这些参数
2. **环境变量后备** - 未提供的参数将自动使用 `.env` 文件中的默认配置
3. **完全可选** - 如果不传任何 MySQL 参数，将完全使用环境变量中的配置
4. **部分覆盖** - 可以只传部分参数，其他参数使用默认配置

**重要**: 最终的配置必须包含 `host`、`user`、`database` 这三个必需参数（可以来自请求参数或环境变量）

### 使用场景

- **多环境部署** - 开发、测试、生产环境使用不同数据库
- **多租户系统** - 不同客户使用不同的数据库
- **数据库切换** - 动态切换到不同的数据库进行查询
- **临时连接** - 临时连接到特定数据库进行测试

## API 端点

### 受保护的端点 (需要 API Key)

- `POST /api/v1/train` - 训练模型 (支持 MySQL 参数)
- `POST /api/v1/ask` - 查询生成 SQL (支持 MySQL 参数)
- `POST /api/v1/mysql/test-connection` - 测试 MySQL 连接 (支持 MySQL 参数)
- `GET /api/v1/files/list` - 获取文件列表
- `GET /api/v1/files/download/<filename>` - 下载文件
- `GET /api/v1/files/preview/<filename>` - 预览文件（图片）
- `POST /api/v1/auth/generate-key` - 生成新的 API Key

### 公开端点 (无需认证)

- `GET /health` - 健康检查
- `POST /api/v1/auth/validate-key` - 验证 API Key 格式

### 可选认证端点

- `GET /api/v1/auth/info` - 获取认证信息

## 管理功能

### 验证 API Key

```bash
curl -X POST http://localhost:8000/api/v1/auth/validate-key \
  -H "Content-Type: application/json" \
  -d '{"api_key": "your-api-key-to-validate"}'
```

### 生成新的 API Key

```bash
curl -X POST http://localhost:8000/api/v1/auth/generate-key \
  -H "Authorization: Bearer your-existing-api-key" \
  -H "Content-Type: application/json" \
  -d '{"length": 32}'
```

### 获取认证信息

```bash
curl -X GET http://localhost:8000/api/v1/auth/info \
  -H "X-API-Key: your-api-key-here"
```

### 测试 MySQL 连接

```bash
# 测试默认配置
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{}'

# 测试自定义配置
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "mysql_host": "localhost",
    "mysql_port": 3306,
    "mysql_user": "test_user",
    "mysql_password": "test_password",
    "mysql_database": "test_db"
  }'
```

### 文件管理

```bash
# 获取文件列表
curl -X GET http://localhost:8000/api/v1/files/list \
  -H "Authorization: Bearer your-api-key"

# 下载 CSV 文件
curl -X GET http://localhost:8000/api/v1/files/download/data.csv \
  -H "Authorization: Bearer your-api-key" \
  -o downloaded_data.csv

# 预览图片
curl -X GET http://localhost:8000/api/v1/files/preview/chart.png \
  -H "Authorization: Bearer your-api-key" \
  -o preview_chart.png
```

## 测试

### 运行认证测试

```bash
python test_api_auth.py
```

### 验证 API Key 格式

```bash
python generate_api_keys.py validate your-api-key-here
```

## 安全建议

1. **使用强 API Keys** - 建议长度至少 32 字符
2. **定期轮换** - 定期更换 API Keys
3. **安全存储** - 不要在代码中硬编码 API Keys
4. **监控日志** - 定期检查认证日志
5. **最小权限** - 为不同用途使用不同的 API Keys

## 错误处理

### 常见错误响应

```json
{
  "success": false,
  "message": "无效的 API Key 或缺少认证信息",
  "error_code": "INVALID_API_KEY",
  "data": null
}
```

### 状态码

- `401` - 认证失败或缺少 API Key
- `400` - 请求参数错误
- `500` - 服务器内部错误

## 禁用认证

如果需要临时禁用认证（仅用于开发环境）：

```bash
ENABLE_API_KEY_AUTH=false
```

⚠️ **警告**: 生产环境中不建议禁用认证功能。

## 故障排除

1. **检查配置** - 确保 `.env` 文件中正确配置了 `API_KEYS`
2. **检查格式** - 确保 API Key 格式正确（16-128字符）
3. **检查头信息** - 确保使用正确的 HTTP 头格式
4. **查看日志** - 检查应用日志中的认证记录
