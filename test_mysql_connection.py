#!/usr/bin/env python3
"""
测试 MySQL 连接接口
"""
import requests
import json
import time


def test_mysql_connection_api():
    """测试 MySQL 连接 API"""
    base_url = "http://localhost:8000/api/v1"
    
    # 请替换为你的实际 API Key
    api_key = "your-api-key-here"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🔍 测试 MySQL 连接接口")
    print("="*60)
    
    # 测试用例
    test_cases = [
        {
            "name": "默认配置连接测试",
            "description": "使用环境变量中的默认配置",
            "data": {}
        },
        {
            "name": "本地 MySQL 连接测试",
            "description": "测试连接到本地 MySQL",
            "data": {
                "mysql_host": "localhost",
                "mysql_port": 3306,
                "mysql_user": "root",
                "mysql_password": "",
                "mysql_database": "test"
            }
        },
        {
            "name": "自定义配置连接测试",
            "description": "测试连接到自定义 MySQL 服务器",
            "data": {
                "mysql_host": "*************",
                "mysql_port": 3306,
                "mysql_user": "test_user",
                "mysql_password": "test_password",
                "mysql_database": "test_db"
            }
        },
        {
            "name": "部分参数测试",
            "description": "只指定主机和数据库",
            "data": {
                "mysql_host": "localhost",
                "mysql_database": "information_schema"
            }
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/mysql/test-connection",
                headers=headers,
                json=test_case['data'],
                timeout=15
            )
            request_time = (time.time() - start_time) * 1000
            
            print(f"   请求时间: {request_time:.2f}ms")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('data', {})
                    mysql_config = data.get('mysql_config', {})
                    server_info = data.get('server_info', {})
                    
                    print("   ✅ 连接成功")
                    print(f"   MySQL 配置:")
                    print(f"     - 主机: {mysql_config.get('host', 'N/A')}")
                    print(f"     - 端口: {mysql_config.get('port', 'N/A')}")
                    print(f"     - 用户: {mysql_config.get('user', 'N/A')}")
                    print(f"     - 数据库: {mysql_config.get('database', 'N/A')}")
                    
                    if server_info:
                        print(f"   服务器信息:")
                        print(f"     - 版本: {server_info.get('version', 'N/A')}")
                    
                    print(f"   数据库存在: {'是' if data.get('database_exists') else '否'}")
                    print(f"   响应时间: {data.get('response_time_ms', 'N/A')}ms")
                else:
                    print("   ❌ 请求失败")
                    print(f"   错误: {result.get('message', 'Unknown error')}")
            else:
                print("   ❌ 连接失败")
                try:
                    error_info = response.json()
                    print(f"   错误: {error_info.get('message', 'Unknown error')}")
                    print(f"   错误代码: {error_info.get('error_code', 'N/A')}")
                except:
                    print(f"   错误: {response.text}")
                    
        except requests.exceptions.Timeout:
            print("   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    # 测试错误情况
    print(f"\n🚫 测试错误情况")
    print("-" * 40)
    
    error_cases = [
        {
            "name": "无效主机",
            "data": {
                "mysql_host": "invalid-host-that-does-not-exist.com",
                "mysql_user": "test",
                "mysql_database": "test"
            }
        },
        {
            "name": "错误端口",
            "data": {
                "mysql_host": "localhost",
                "mysql_port": 9999,
                "mysql_user": "test",
                "mysql_database": "test"
            }
        },
        {
            "name": "错误用户名",
            "data": {
                "mysql_host": "localhost",
                "mysql_user": "nonexistent_user",
                "mysql_password": "wrong_password",
                "mysql_database": "test"
            }
        },
        {
            "name": "不存在的数据库",
            "data": {
                "mysql_host": "localhost",
                "mysql_user": "root",
                "mysql_database": "nonexistent_database_12345"
            }
        }
    ]
    
    for i, test_case in enumerate(error_cases, 1):
        print(f"\n  {i}. {test_case['name']}")
        
        try:
            response = requests.post(
                f"{base_url}/mysql/test-connection",
                headers=headers,
                json=test_case['data'],
                timeout=15
            )
            
            print(f"     状态码: {response.status_code}")
            
            if response.status_code == 400:
                print("     ✅ 正确返回错误状态")
                try:
                    error_info = response.json()
                    print(f"     错误信息: {error_info.get('message', 'N/A')}")
                except:
                    pass
            else:
                print("     ❓ 意外的状态码")
                
        except requests.exceptions.Timeout:
            print("     ⏰ 连接超时（预期行为）")
        except Exception as e:
            print(f"     ❌ 异常: {str(e)}")


def show_usage_examples():
    """显示使用示例"""
    print(f"\n" + "="*60)
    print("📚 使用示例")
    print("="*60)
    
    print("\n1. 测试默认配置:")
    print("""
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{}'
""")
    
    print("\n2. 测试自定义配置:")
    print("""
curl -X POST http://localhost:8000/api/v1/mysql/test-connection \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "mysql_host": "localhost",
    "mysql_port": 3306,
    "mysql_user": "test_user",
    "mysql_password": "test_password",
    "mysql_database": "test_db"
  }'
""")
    
    print("\n3. Python 代码示例:")
    print("""
import requests

headers = {"Authorization": "Bearer your-api-key"}
data = {
    "mysql_host": "localhost",
    "mysql_user": "root",
    "mysql_database": "test"
}

response = requests.post(
    "http://localhost:8000/api/v1/mysql/test-connection",
    headers=headers,
    json=data
)

result = response.json()
if result['success']:
    print("连接成功!")
    print(f"服务器版本: {result['data']['server_info']['version']}")
else:
    print(f"连接失败: {result['message']}")
""")
    
    print(f"\n💡 提示:")
    print("- 此接口只测试连接，不会执行任何数据操作")
    print("- 可以用于验证 MySQL 配置是否正确")
    print("- 支持检测数据库是否存在")
    print("- 返回详细的错误信息帮助排查问题")


if __name__ == '__main__':
    try:
        test_mysql_connection_api()
        show_usage_examples()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n❌ 测试错误: {str(e)}")
        print("请确保:")
        print("1. 服务器正在运行在 http://localhost:8000")
        print("2. 已配置有效的 API Key")
        print("3. 网络连接正常")
