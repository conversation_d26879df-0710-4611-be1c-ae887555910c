#!/usr/bin/env python3
"""
API Key 生成工具
用于生成新的 API Keys 并提供管理功能
"""
import sys
import argparse
from app.auth import APIKeyManager


def generate_keys(count=1, length=32):
    """生成指定数量的 API Keys"""
    print(f"生成 {count} 个长度为 {length} 的 API Keys:")
    print("-" * 50)
    
    keys = []
    for i in range(count):
        key = APIKeyManager.generate_api_key(length)
        keys.append(key)
        print(f"{i+1:2d}. {key}")
    
    print("-" * 50)
    print("\n环境变量格式:")
    print(f"API_KEYS={','.join(keys)}")
    
    return keys


def validate_key(api_key):
    """验证 API Key 格式"""
    is_valid, message = APIKeyManager.validate_api_key_format(api_key)
    
    print(f"API Key: {api_key}")
    print(f"格式验证: {'✅ 有效' if is_valid else '❌ 无效'}")
    print(f"消息: {message}")
    
    if is_valid:
        print(f"长度: {len(api_key)} 字符")
        print(f"哈希值: {APIKeyManager.hash_api_key(api_key)}")
    
    return is_valid


def main():
    parser = argparse.ArgumentParser(description='API Key 管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成命令
    generate_parser = subparsers.add_parser('generate', help='生成新的 API Keys')
    generate_parser.add_argument('-c', '--count', type=int, default=1, 
                               help='生成的 API Key 数量 (默认: 1)')
    generate_parser.add_argument('-l', '--length', type=int, default=32,
                               help='API Key 长度 (默认: 32)')
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证 API Key 格式')
    validate_parser.add_argument('api_key', help='要验证的 API Key')
    
    # 批量生成命令
    batch_parser = subparsers.add_parser('batch', help='批量生成不同长度的 API Keys')
    batch_parser.add_argument('--admin', action='store_true', 
                            help='生成管理员级别的长 API Key')
    batch_parser.add_argument('--user', action='store_true',
                            help='生成用户级别的标准 API Key')
    batch_parser.add_argument('--service', action='store_true',
                            help='生成服务级别的短 API Key')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'generate':
        if args.count < 1 or args.count > 100:
            print("❌ 错误: 生成数量必须在 1-100 之间")
            return
        
        if args.length < 16 or args.length > 128:
            print("❌ 错误: API Key 长度必须在 16-128 之间")
            return
        
        generate_keys(args.count, args.length)
    
    elif args.command == 'validate':
        validate_key(args.api_key)
    
    elif args.command == 'batch':
        print("批量生成 API Keys:")
        print("=" * 60)
        
        all_keys = []
        
        if args.admin or not any([args.admin, args.user, args.service]):
            print("\n🔑 管理员级别 API Keys (64字符):")
            admin_keys = generate_keys(2, 64)
            all_keys.extend(admin_keys)
        
        if args.user or not any([args.admin, args.user, args.service]):
            print("\n👤 用户级别 API Keys (32字符):")
            user_keys = generate_keys(3, 32)
            all_keys.extend(user_keys)
        
        if args.service or not any([args.admin, args.user, args.service]):
            print("\n🔧 服务级别 API Keys (24字符):")
            service_keys = generate_keys(2, 24)
            all_keys.extend(service_keys)
        
        print("\n" + "=" * 60)
        print("所有生成的 API Keys:")
        print(f"API_KEYS={','.join(all_keys)}")
        
        print(f"\n📊 统计:")
        print(f"总计生成: {len(all_keys)} 个 API Keys")
        print(f"建议用途:")
        print(f"  - 长密钥 (64字符): 管理员操作、敏感功能")
        print(f"  - 中密钥 (32字符): 普通用户、日常操作")
        print(f"  - 短密钥 (24字符): 服务间调用、自动化脚本")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        sys.exit(1)
